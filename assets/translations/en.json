{"You have to minimum ": "You have to minimum ", "wallet amount to receiving Order": "wallet amount to receiving Order", "Trip Distance": "Trip Distance", "Delivery charge": "Delivery charge", "Reject": "Reject", "This Ride is already reject by customer.": "This Ride is already reject by customer.", "Rejecting Ride...": "Rejecting Ride...", "Accept": "Accept", "This Ride is reject by customer.": "This Ride is reject by customer.", "Accepting Ride....": "Accepting Ride....", "Verify Code to customer": "Verify Code to customer", "Pickup Customer": "Pickup Customer", "Reached To destination": "Reached To destination", "Complete Ride": "Complete Ride", "ORDER ID ": "ORDER ID ", "Message": "Message", "CALL": "CALL", "Destination": "Destination", "Customer payment is pending.": "Customer payment is pending.", "Updating Ride...": "Updating Ride...", "Ride update...": "Ride update...", "Completing Delivery...": "Completing Delivery...", "Please wait": "Please wait", "Going online...": "Going online...", "Ride Detail": "Ride Detail", "Paid": "Paid", "UnPaid": "UnPaid", "Admin commission": "Admin commission", "Note : Admin commission will be debited from your wallet balance. Admin commission will apply on order Amount minus Discount (if applicable).": "Note : Admin commission will be debited from your wallet balance. Admin commission will apply on order Amount minus Discount (if applicable).", "Cab Details :": "Cab Details :", "Payment Details": "Payment Details", "Sub Total": "Sub Total", "Discount": "Discount", "Tip": "Tip", "Total": "Total", "No Rides Found": "No Rides Found", "Home": "Home", "Background Location permission": "Background Location permission", "This app collects location data to enable location fetching at the time of you are on the way to deliver order or even when the app is in background.": "This app collects location data to enable location fetching at the time of you are on the way to deliver order or even when the app is in background.", "Okay": "Okay", "Press Back button again to Exit": "Press Back button again to Exit", "Online": "Online", "Rides": "Rides", "Wallet": "Wallet", "Earnings": "Earnings", "Withdraw method": "Withdraw method", "Bank Info": "Bank Info", "Settings": "Settings", "Profile": "Profile", "My Profile": "My Profile", "Language": "Language", "Terms and Condition": "Terms and Condition", "Privacy policy": "Privacy policy", "Inbox": "Inbox", "My Inbox": "My Inbox", "Log out": "Log out", "Cab Booking not found": "<PERSON><PERSON> Booking not found", "Ride setting": "Ride setting", "Intercity / OutStation": "Intercity / OutStation", "Both": "Both", "both": "both", "ride": "ride", "Setting successfully update.": "Setting successfully update.", "Save": "Save", "Collect OTP from customer": "Collect OTP from customer", "Verify OTP": "Verify OTP", "OTP verify": "OTP verify", "OTP Invalid": "OTP Invalid", "Failed": "Failed", "Rejecting order...": "Rejecting order...", "Accepting order...": "Accepting order...", "Pick up Parcel": "Pick up <PERSON><PERSON><PERSON>", "Parcel delivery": "Parcel delivery", "Payment Collect by Receiver": "Payment Collect by Receiver", "Sender Name": "Sender Name", "Receiver Name": "Receiver Name", "Updating order...": "Updating order...", "Refund Amount": "Refund Amount", "Pickup Parcel": "Pickup <PERSON><PERSON>", "Oder Detail": "Oder Detail", "Weight": "Weight", "Rate": "Rate", "Your Customer": "Your Customer", "Order Summary": "Order Summary", "Subtotal": "Subtotal", "Sender": "Sender", "Receiver": "Receiver", "No Previous Orders": "No Previous Orders", "Let's deliver food!": "Let's deliver food!", "Total : ": "Total : ", "Parcel Type : ": "Parcel Type : ", "Order Status": "Order Status", "Order Placed": "Order Placed", "Driver Pending": "Driver Pending", "Order Ready to Pickup": "Order Ready to Pickup", "In Transit": "In Transit", "Order Rejected": "Order Rejected", "Order Completed": "Order Completed", "Order Date": "Order Date", "PickUp date": "PickUp date", "Drop Date": "Drop Date", "Parcel Orders": "Parcel Orders", "Booking": "Booking", "Booking not found": "Booking not found", "With driver trip": "With driver trip", "PickUp": "PickUp", "Drop off": "Drop off", "Booking Details": "Booking Details", "Booking summary": "Booking summary", "Driver Amount": "Driver Amount", "Driver by": "Driver by", "Completed": "Completed", "On Ride": "On Ride", "Canceled": "Canceled", "Pending": "Pending", "All": "All", "Ongoing": "Ongoing", "Decline": "Decline", "You can start ride on your pickup date and time.": "You can start ride on your pickup date and time.", "Start Ride": "Start Ride", "Booking History": "Booking History", "Please wait...": "Please wait...", "updated transaction": "updated transaction", "Uploading image...": "Uploading image...", "Uploading image ": "Uploading image ", "Uploading video...": "Uploading video...", "Uploading video": "Uploading video", "Uploading Audio...": "Uploading Audio...", "Referral Amount": "Referral Amount", "Couldn't login with apple.": "Couldn't login with apple.", "Couldn't sign up": "Couldn't sign up", "Email address is malformed.": "Email address is malformed.", "No user corresponding to the given email address.": "No user corresponding to the given email address.", "This user has been disabled.": "This user has been disabled.", "Too many attempts to sign in as this user.": "Too many attempts to sign in as this user.", "Unexpected firebase error, Please try again.": "Unexpected firebase error, Please try again.", "Login failed, Please try again.": "<PERSON><PERSON> failed, Please try again.", "Uploading car image, Please wait...": "Uploading car image, Please wait...", "Couldn't create new user with phone number.": "Couldn't create new user with phone number.", "Uploading image, Please wait...": "Uploading image, Please wait...", "Couldn't sign up for firebase, Please try again.": "Couldn't sign up for firebase, Please try again.", "Email already in use, Please pick another email!": "Email already in use, Please pick another email!", "Email/password accounts are not enabled": "Email/password accounts are not enabled", "Password must be more than 5 characters": "Password must be more than 5 characters", "Too many requests, Please try again later.": "Too many requests, Please try again later.", "Enter valid e-mail": "Enter valid e-mail", "Couldn\\'t sign up for firebase, Please try again.": "", "Name is required": "Name is required", "Name must be valid": "Name must be valid", "Mobile is required": "Mobile is required", "Mobile Number must be digits": "Mobile Number must be digits", "please enter valid number": "please enter valid number", "*required": "*required", "Password length must be more than 6 chars.": "Password length must be more than 6 chars.", "Please use a valid mail": "Please use a valid mail", "Password must match": "Password must match", "Confirm password is required": "Confirm password is required", "This field can't be empty.": "This field can't be empty.", "OK": "OK", "Yesterday At {}": "Yesterday At {}", "Location permissions are denied": "Location permissions are denied", "Location permissions are permanently denied, we cannot request permissions.": "Location permissions are permanently denied, we cannot request permissions.", "Withdrawal Details": "<PERSON><PERSON><PERSON>", "Transaction ID": "Transaction ID", "Date": "Date", "Note": "Note", "Admin Note": "Admin Note", "Cancel Payment": "Cancel Payment", "cancelPayment?": "cancel Payment?", "Cancel": "Cancel", "Continue": "Continue", "Account Details": "Account Details", "Public Info": "Public Info", "First Name": "First Name", "Last Name": "Last Name", "Car Model": "Car Model", "Car Plate": "Car Plate", "Private Details": "Private Details", "Email Address": "Email Address", "Login with E-mail": "Login with E-mail", "Sign up with E-mail": "Sign up with E-mail", "Pickup Car proof": "Pickup Car proof", "Pickup Driver proof": "Pickup Driver proof", "Password": "Password", "Confirm Password": "Confirm Password", "Forgot password?": "Forgot password?", "Phone Number": "Phone Number", "Saving details...": "Saving details...", "Details saved successfully": "Details saved successfully", "Couldn't save details, Please try again": "Couldn't save details, Please try again", "Welcome to Ta entregue Driver": "Bem vindo ao Ta Entregue", "Make extra cash by delivery orders to our customers.": "Make extra cash by delivery orders to our customers.", "Log In": "Log In", "Sign Up": "Sign Up", "Bank Name": "Bank Name", "Branch Name": "Branch Name", "Holder Name": "Holder Name", "Account Number": "Account Number", "Other Information": "Other Information", "EDIT BANK": "EDIT BANK", "You have not added bank account": "You have not added bank account", "please add your bank account": "please add your bank account", "ADD BANK": "ADD BANK", "Add Bank": "Add Bank", "Edit Bank": "Edit Bank", "Withdraw method saved successfully": "Withdraw method saved successfully", "Couldn't save details, Please try again.": "Couldn't save details, Please try again.", "No Conversion found": "No Conversion found", "Start typing ...": "Start typing ...", "sent An Image": "Send An Image", "sent A Video": "Send A Video", "sent A VoiceMessage": "Send A Voice Message", "sendMedia": "Send Media", "chooseImageFromGallery": "Choose Image From Gallery", "chooseVideoFromGallery": "Choose Video From Gallery", "takeAPicture": "Take A Picture", "recordVideo": "Record Video", "Contact Us": "Contact Us", "Deleting account...": "Deleting account...", "Logout": "Logout", "Delete Account": "Delete Account", "Our Address": "Our Address", "Email Us": "Email Us", "Orders": "Orders", "Capture Picture": "Capture Picture", "REACHED STORE FOR PICKUP": "REACHED STORE FOR PICKUP", "Deliver to {}": "Deliver to {}", "REACHED CUSTOMER DOOR STEP": "REACHED CUSTOMER DOOR STEP", "DELIVER": "DELIVER", "Deliver": "Deliver", "ITEMS": "ITEMS", "Given": "Given", "item to customer": "item to customer", "MARK ORDER DELIVER": "MARK ORDER DELIVER", "Pick": "Pick", "Order ready, Pick now !": "Order ready, Pick now !", "Confirm Items": "Confirm Items", "PICKED ORDER": "PICKED ORDER", "Language change successfully": "Language change successfully", "OR": "OR", "Facebook Login": "Facebook Login", "Login with phone number": "Login with phone number", "Logging in, please wait...": "Logging in, please wait...", "This account is not active please contact administrator": "This account is not active please contact administrator", "Driver is not activated yet. Please contact to admin to activate it. Thanks.": "Driver is not activated yet. Please contact to admin to activate it. Thanks.", "Couldn't Authenticate": "Couldn't Authenticate", "Couldn't login with facebook.": "Couldn't login with facebook.", "Error": "Error", "Add Address": "Add Address", "Choose Your Favorite Item": "Choose Your Favorite Item", "Fastest Delivery": "Fastest Delivery", "Find perfect store nearby or  place order at your favorite store in few clicks.": "Find perfect store nearby or  place order at your favorite store in few clicks.", "A diverse list of different dining stores throughout the territory and around your area carefully selected": "A diverse list of different dining stores throughout the territory and around your area carefully selected", "Get your favorite item fastest delivered at your doorstep": "Get your favorite item fastest delivered at your doorstep", "GET STARTED": "GET STARTED", "SKIP": "SKIP", "NEXT": "NEXT", "Delivery service": "Delivery service", "Cab service": "Cab service", "Parcel service": "Parcel service", "Rental Service": "Rental Service", "Sign In": "Sign In", "Create new account": "Create new account", "Login": "<PERSON><PERSON>", "Please choose a service type.": "Please choose a service type.", "Login, Please Wait....": "<PERSON><PERSON>, Please Wait....", "Creating new account, Please wait...": "Creating new account, Please wait...", "Couldn't Log In": "Couldn't Log In", "Otp Invalid": "Otp Invalid", "Add your Vehicle image.": "Add your Vehicle image.", "Choose image from gallery": "Choose image from gallery", "Take a picture": "Take a picture", "Add profile picture": "Add profile picture", "Add Car Image": "Add Car Image", "Choose from gallery": "Choose from gallery", "Remove picture": "Remove picture", "Select Section": "Select Section", "field required": "field required", "Select vehicle type": "Select vehicle type", "Select Car Makes": "Select Car Makes", "Select Car Model": "Select Car Model", "Car Color": "Car Color", "error: ": "error: ", "Add Profile Picture": "Add Profile Picture", "Add Car Picture": "Add Car Picture", "Removing Picture...": "Removing Picture...", "Uploading car image...": "Uploading car image...", "Please Re-Authenticate in order to perform this action.": "Please Re-Authenticate in order to perform this action.", "Verify": "Verify", "Facebook Verify": "Facebook Verify", "Apple sign in is not available on this device.": "Apple sign in is not available on this device.", "Empty Password": "Empty Password", "Password is required to update email": "Password is required to update email", "Verifying...": "Verifying...", "Couldn't verify": "Couldn't verify", "Please double check the password and try again.": "Please double check the password and try again.", "Couldn't verify, Please try again.": "Couldn't verify, Please try again.", "Couldn't verify with facebook.": "Couldn't verify with facebook.", "Couldn't verify with apple.": "Couldn't verify with apple.", "Sending code...": "Sending code...", "Code verification timeout, request new code.": "Code verification timeout, request new code.", "An error has occurred, please try again.": "An error has occurred, please try again.", "Invalid code or has been expired.": "Invalid code or has been expired.", "Couldn't get verification ID": "Couldn't get verification ID", "Reset Password": "Reset Password", "E-mail": "E-mail", "Send Link": "Send Link", "Sending Email...": "Sending Email...", "Please check your email.": "Please check your email.", "Push Notifications": "Push Notifications", "Allow Push Notifications": "Allow Push Notifications", "Order Updates": "Order Updates", "Promotions": "Promotions", "Saving changes...": "Saving changes...", "Settings saved successfully": "Setting<PERSON> saved successfully", "Sign up with phone number": "Sign up with phone number", "individual": "individual", "TopUp History": "TopUp History", "Something went wrong": "Something went wrong", "No Transaction History": "No Transaction History", "Wallet Topup": "<PERSON><PERSON>", "Wallet Amount Deducted": "Wallet Amount Deducted", "Transaction Details": "Transaction Details", "Pay Via": "Pay Via", "Date in UTC Format": "Date in UTC Format", "Payment": "Payment", "Exit": "Exit", "Continue Payment": "Continue Payment", "Total Balance": "Total Balance", "error": "error", "WITHDRAW": "WITHDRAW", "Please add your Withdraw method first": "Please add your Withdraw method first", "WITHDRAWAL HISTORY": "WITHDRAWAL HISTORY", "TOPUP WALLET": "TOPUP WALLET", "TOPUP HISTORY": "TOPUP HISTORY", "Topup Wallet": "Topup Wallet", "Add Topup Amount": "Add Topup Amount", "*required Field": "*required Field", "Select Payment Option": "Select Payment Option", "Stripe": "Stripe", "PayStack": "PayStack", "FlutterWave": "FlutterWave", "RazorPay": "RazorPay", "Pay Fast": "Pay Fast", "Paytm": "Paytm", "MercadoPago": "MercadoPago", "PayPal": "PayPal", "Payment Successful!!": "Payment Successful!!", "Payment Unsuccessful!!": "Payment Unsuccessful!!", "Something went wrong, please contact admin.": "Something went wrong, please contact admin.", "CONTINUE": "CONTINUE", "No Response!": "No Response!", "Error while transaction!": "Error while transaction!", "Status :": "Status :", "Status : Payment Incomplete!!": "Status : Payment Incomplete!!", "Daily": "Daily", "Monthly": "Monthly", "Yearly": "Yearly", "Order Amount": "Order Amount", "Admin commission Deducted": "Admin commission Deducted", "View Order": "View Order", "Payment Processing Via": "Payment Processing Via", "Payment Failed!!": "Payment Failed!!", "Withdraw": "Withdraw", "Amount to Withdraw": "Amount to Withdraw", "*Invalid Amount": "*In<PERSON>id Amount", "*withdraw is more then wallet balance": "*withdraw is more then wallet balance", "Add note": "Add note", "Failed!": "Failed!", "Withdraw amount must be greater or equal to": "Withdraw amount must be greater or equal to", "Please wait!!": "Please wait!!", "Please wait!! while completing Transaction": "Please wait!! while completing Transaction", "Ta entregue Driver": "<PERSON>"}