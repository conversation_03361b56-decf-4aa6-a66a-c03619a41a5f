{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9820c4e1522acc33d585964464c667c3e1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986fd1e6d701cee2019c4ffcc8be24662e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985989ed889e7ef3101e535e41501e63bc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987fbbcd9f52ffc8d7270fd0d309918d48", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985989ed889e7ef3101e535e41501e63bc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f18034db5e36d2954d13efe24565cae", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980034b7529331ef33e8d261c73ba1fc98", "guid": "bfdfe7dc352907fc980b868725387e9871d91a42be601b71d307300b8355ee1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803d597d055ce9f51ff1802dc79dd63bb", "guid": "bfdfe7dc352907fc980b868725387e983b665e94a4cfaa5b6a0aca002e7e59fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986494a08be86baa4038fe08bb1a5139d2", "guid": "bfdfe7dc352907fc980b868725387e9806e4e0f07e830dcf1c064d4fde7372c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d860a0f0e7759ab3e97583814b6bb660", "guid": "bfdfe7dc352907fc980b868725387e98396b98354e97c6b1358117f08a4e306a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869a2a91e248754d0ed36b896aecb79c6", "guid": "bfdfe7dc352907fc980b868725387e987ac25a11fa40f178db5d50bff061db77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98938a8a2610f4b9f438066c381790dedc", "guid": "bfdfe7dc352907fc980b868725387e98f3b1973278086302771042df4e00ea41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc844cd896554796b970b20f8b2ae5ff", "guid": "bfdfe7dc352907fc980b868725387e98756f5b397ad053077de8f46d7da8fab2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989282dc33b41861a6f26a675d80b09a0b", "guid": "bfdfe7dc352907fc980b868725387e98b1c7b574f15f7fc9aa11534e7d2b7562", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9eb945a6ed829280a1b126799d2d1ec", "guid": "bfdfe7dc352907fc980b868725387e9834540f115b38beb81f79aaec362acee4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caca812670f1b493925aeb74fbc547aa", "guid": "bfdfe7dc352907fc980b868725387e987738e43885705e2ca3d3f952a852d1e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98532c20d55b64ab1583a2bdb339a3f7c5", "guid": "bfdfe7dc352907fc980b868725387e98039ec9cf2443d2dfcaa6bfd00745a082", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868c978b056c2c9e0d2b3a4ddb340b433", "guid": "bfdfe7dc352907fc980b868725387e980b651aa249a332add6784e1a51326269", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845275bbb2e7ae67e59870f21ef515633", "guid": "bfdfe7dc352907fc980b868725387e988badb0d31d2706b6e4f9747d22135a0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802db2acdd5d23c94065fb950a201b8cb", "guid": "bfdfe7dc352907fc980b868725387e983155fd9f6a3b8bca94c3c79ab7d6f172", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a77386e84dfba3bddd8afb7bc057f679", "guid": "bfdfe7dc352907fc980b868725387e98900d260dc13755009dc5e9a87b094ce7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de1c8cd69f0d791ed2ea4b511b67314b", "guid": "bfdfe7dc352907fc980b868725387e9828b6a38764c56d071ba48fdbb255f533"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5be44120bbda9a3114d21ef5cc6abbe", "guid": "bfdfe7dc352907fc980b868725387e98f05d93bb93fdddbb0e4e7929359c57b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850b8d87a67327c8021ccc52f66bac70c", "guid": "bfdfe7dc352907fc980b868725387e983a4581eb617a5ad7d83892d698d594b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98417a4d91682f81552d1656be92f7b51c", "guid": "bfdfe7dc352907fc980b868725387e9849ccab35060d3b04350d5b2ec96a6be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882ea5b18629c1869208c086622d9f430", "guid": "bfdfe7dc352907fc980b868725387e98ee4dfe3e5170a4d901e0ea6c5751579f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c6f1c9e48e821ffd7c0576cda9535e3", "guid": "bfdfe7dc352907fc980b868725387e9885d6d8d78881bf3885d75b32e70b1277"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98909106be0c41a9059477b0e7a1da5cea", "guid": "bfdfe7dc352907fc980b868725387e98764cfede42317a5d2c6a5172733c7e97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff67700d7a037cec1d4c641ad16be6a9", "guid": "bfdfe7dc352907fc980b868725387e98ce2868d00d05d2d2211051232eb2df1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b820fef84e1c6e32f58b3870ea1da7ca", "guid": "bfdfe7dc352907fc980b868725387e98cd6526b0c21c31085bd5878a26ea1a52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98218c939057a2f2d442c5410cd4d4a86f", "guid": "bfdfe7dc352907fc980b868725387e9889255affc0e671e70bb08e4e4f3f6e7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856b6dbd38c15a8ad8a2b4a26f1420302", "guid": "bfdfe7dc352907fc980b868725387e9815dca42c43fccc27a7b13ec4db07705a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989795a0217094d1884a320815788e0bc1", "guid": "bfdfe7dc352907fc980b868725387e9879c2596fd3a27cc64d264f803ac04a3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3aabc77eb83889d31e647e251b6823c", "guid": "bfdfe7dc352907fc980b868725387e98f42c7113b7322f4073dcac9b6e96d7d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b54b88a25c9bf2adb8a06de4084403a", "guid": "bfdfe7dc352907fc980b868725387e9812f35353a4585fb67a3e91a122e4961f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0a59b4d8f5b2376342b42a4eb303f0b", "guid": "bfdfe7dc352907fc980b868725387e98ede8ff609a807a6a6f7f3501653a1530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828b84a05af90f3039e4d4f566e81b2aa", "guid": "bfdfe7dc352907fc980b868725387e981f72ce5bfe0f0920fcf3a20705e8519e"}], "guid": "bfdfe7dc352907fc980b868725387e989a8bdd7db64c936feeb9fdccde76a124", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98acf1495821301a2e018d066a3bbc0873", "guid": "bfdfe7dc352907fc980b868725387e98f2d8328aa3716bcf58165cb6aabdd9eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f075b69ae15a8facd0d1ca832e1c856e", "guid": "bfdfe7dc352907fc980b868725387e980a6842fc579249ca27117522fcb32520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc280d7c7192f645111a95020108ab1c", "guid": "bfdfe7dc352907fc980b868725387e98c5bcc8cfdebaa2ea4c37d0e8cf801bb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802d9422dc3a9d700a99656173d99a20a", "guid": "bfdfe7dc352907fc980b868725387e980d69a15c5090c41cfe80991e6b959490"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98469c5f07757fef6d59ae5d308563e6a2", "guid": "bfdfe7dc352907fc980b868725387e987c40da3cb9a8bee11591bb9fcfbf9747"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e775e92350209fd78a9460de8140727", "guid": "bfdfe7dc352907fc980b868725387e981e211ce9b5a82c38a89df7432af5ae11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98848ef947277101f8a19ed5c44600202d", "guid": "bfdfe7dc352907fc980b868725387e983e0ead0ddd47065d3ee3316a6a456558"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819e9146630a1daec942f60a39a2ba4fc", "guid": "bfdfe7dc352907fc980b868725387e98a10b53c251fead24e2bc80ffabaca7e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ddbff3c0d23c1632db118d4ccece513", "guid": "bfdfe7dc352907fc980b868725387e98908b25b6de0d398e2db2ab88c7de1b95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885496d972ed0306579856771d24f3186", "guid": "bfdfe7dc352907fc980b868725387e981532a1ac14391b4b492680fe682b0957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8c35f7cc7ffe991c49603d5c6224c95", "guid": "bfdfe7dc352907fc980b868725387e9818ac9bb25ea11222d5e6d5d5016eeec6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f63c21da6d8db61fc8621bc50092fe9e", "guid": "bfdfe7dc352907fc980b868725387e98aace04b8dc34a35748654fa1fa5d33a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98252a1bdf66de49447d5ce1ae0ea1e54d", "guid": "bfdfe7dc352907fc980b868725387e980435a41cdea476bf7b11f4bfcd411ec4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c8d41dd2c5bca9eff538d68f8965852", "guid": "bfdfe7dc352907fc980b868725387e984695a5858a5c3c95172aebe1fe52346f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981709ef593c450931c19fd827fe2d0b19", "guid": "bfdfe7dc352907fc980b868725387e98fad322e08034ceb08d597725afeba077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b392b681afb5d0ceb160a7e490230cd", "guid": "bfdfe7dc352907fc980b868725387e98b739033624abbc444cdbd9c09bdf3e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee0ebf5191d11606a83d8fa3650635d3", "guid": "bfdfe7dc352907fc980b868725387e9840a011651d32a8a7d65ff37efb37da99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efa51b6257b80eef4a8f171b0df16668", "guid": "bfdfe7dc352907fc980b868725387e98a2eb62cb148ada087de53721959663a9"}], "guid": "bfdfe7dc352907fc980b868725387e980f6ad4bb86e2126a469e3678db6ab533", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e989ad225b4241890eee92fd5876f740106"}], "guid": "bfdfe7dc352907fc980b868725387e980c126ec4e291ef8d9fbbbb38989292f4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9808b47889de662cd4865776eda02c0c2b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore"}, {"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98ecaebc2b66f6675fbaa388164aa6c8dd", "name": "FirebaseAppCheck", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9857de7acecfe5aa305e96dd28add8de37", "name": "FirebaseAppCheck.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}