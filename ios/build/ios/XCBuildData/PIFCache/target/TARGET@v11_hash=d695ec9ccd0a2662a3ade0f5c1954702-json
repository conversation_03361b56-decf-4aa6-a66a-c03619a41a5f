{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c0394a6c27864397d8d5bb9b08704010", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f6c31a70af2ba7e99dcc497508f5a875", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989581669dcc44ed9cbaad8b3724a3aaae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98421f556956b806724bc829cf6973c7c4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989581669dcc44ed9cbaad8b3724a3aaae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983669e251cae738fcdb9f8889d7d6d690", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98772c53405ec5a1345ec907d5d3cae57c", "guid": "bfdfe7dc352907fc980b868725387e98baa60214dbcac402b25f320f693b3548", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866798f8d72e51aa9662fd819f75a75ea", "guid": "bfdfe7dc352907fc980b868725387e98aa6f73cdaf67eb16e5dc9a830cab76ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a491681648a8a3a5610164aceb1551f", "guid": "bfdfe7dc352907fc980b868725387e98c84de7526bf59fcb6b382f5eb48a1b9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899f7c5b5ea3968b66ecf73417527ac9e", "guid": "bfdfe7dc352907fc980b868725387e980a9bb0f509b0c0c78839f46db09a21f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c721a8e18bdaa3a32600e3dcb22d0f63", "guid": "bfdfe7dc352907fc980b868725387e98050cd988b98a79d4796bed1883cee6ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f46c5e9fb28eecd384ee346a549cd412", "guid": "bfdfe7dc352907fc980b868725387e98cac60d15a0ba4a615f2ce3880dfe95e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cd0749bc09a9968f9361097f18e6a19", "guid": "bfdfe7dc352907fc980b868725387e98eb418b5653b777b499c5eb9a425a9929", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f3a4517459ac899680866f95b4d1d52", "guid": "bfdfe7dc352907fc980b868725387e98b7e37d1feae2ad88982b042b16407eba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835596fc8c47af5434291c875080d322a", "guid": "bfdfe7dc352907fc980b868725387e98f9b7397b24611d9d516109c5b3dd2532", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981e616098c64a696f1f6c4837cce751ae", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9829dd5fe36b2a5714258a7406ff06182d", "guid": "bfdfe7dc352907fc980b868725387e98c42d3884102a546876506fca99c049b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fa49beda210f52c5fd1ca651e4c3fcc", "guid": "bfdfe7dc352907fc980b868725387e9816368c03287392739d6b85be770b6711"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bdb12e09bb8fb9cad02cd5166ad2fd9", "guid": "bfdfe7dc352907fc980b868725387e98dec8eb4ebf6648490db4a9608e47960e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbc2b43f0da242995b70c46cc3572549", "guid": "bfdfe7dc352907fc980b868725387e983f8bedef0615f3d444cd192a18141eda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820b27cd85e9d60fb78c2b30e747b4f9a", "guid": "bfdfe7dc352907fc980b868725387e98c5f753bc7e5745ddfb6257c0f61b3922"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986984e198729242beec1cedfc8f8b4be2", "guid": "bfdfe7dc352907fc980b868725387e98657b8e330cead85d0aa38bc7d90a57e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c873f2731216f448362542457bd3212", "guid": "bfdfe7dc352907fc980b868725387e986c4be10229b80069a401ecfeba990ee3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff0a270aebf09eb23861bcb2d1aa3d37", "guid": "bfdfe7dc352907fc980b868725387e98df83ffcb263e182aa5cbbb3870b6ee96"}], "guid": "bfdfe7dc352907fc980b868725387e98cc36c323cdf314d72c81f4479e82f0b0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e9895c90c9f9161d70256fc75e83b08c37e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d05a97faeb775352b784fcd3a04da5fd", "guid": "bfdfe7dc352907fc980b868725387e983251c6649fc8556e711691aef86545f8"}], "guid": "bfdfe7dc352907fc980b868725387e98909ea38205f4741fa6a909a6f22e4bf6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98783b4819704dc4a8822dfeba27560746", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98b44210eb3c85a15c99b7dee2a0a9f224", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e987d0793544873023f7c0cf57e1ebe61c5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}