{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a8827aafa8d7d4c0a2d5901ab0189bc2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e9a5491f4c8e6fcb2e240d0f6b52ede1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a395c2771617c3c31958a738db4a7a6c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989210123f292311a02de269bd2187600a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a395c2771617c3c31958a738db4a7a6c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983fd35ce98aedc22fdfb8f8a152f2fc59", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982b4f13b87af0310813947609ad4a026b", "guid": "bfdfe7dc352907fc980b868725387e9881373fb98f5987f47edf366b6f2e88a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a956469488cc8712d80eb1b7803e4646", "guid": "bfdfe7dc352907fc980b868725387e983378e74ee8cc0e39ac49371a045d48b4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98efe21a48aa6ab29583dccf914bd1e3aa", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e8588dd5e014c875b1b212770d03658a", "guid": "bfdfe7dc352907fc980b868725387e98e8110716c64c80c55ff68b2a348fe68e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d054f746fbd24549eaeba47f2f45b90", "guid": "bfdfe7dc352907fc980b868725387e98976a4098f43691753ef56557d4037ecd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984152a84df88b189badaaca8274acd164", "guid": "bfdfe7dc352907fc980b868725387e98f699352212cf77dc1fffbd37c646abb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848e79d9d0c5b1216da2912b4de5d904d", "guid": "bfdfe7dc352907fc980b868725387e9831d0d14f477ea081ed53b99a1cd62994"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cef484d73f8335235af5cc9b9017968", "guid": "bfdfe7dc352907fc980b868725387e988ec3e9e3ac57da38456e47998f8ed123"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f15a48e30fd87e42e82882ea28f6969", "guid": "bfdfe7dc352907fc980b868725387e98b38ac48376f772bda0b74f61e7368e51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986891bf8166e63d91fc191806fd7e05b0", "guid": "bfdfe7dc352907fc980b868725387e98f4d806e16470b6f1e3368dabbf6f95a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9e8e1285d32d6414d5112b4261c6166", "guid": "bfdfe7dc352907fc980b868725387e9839771dcfed64ddcc6de95aa9edb50470"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d7e74e80a8d7d54c1c2f9be6c7f1f36", "guid": "bfdfe7dc352907fc980b868725387e98b8d9fc3a199aa605ac2cc581a6d8ded2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef3e02c43cbc76ff7c1d758f16d99c0e", "guid": "bfdfe7dc352907fc980b868725387e98afdf4432c350e13ecd4795e39db4eb9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af2809dd55a46fe1d822c531d426f521", "guid": "bfdfe7dc352907fc980b868725387e98a88318139799612336b6392f182a05a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf30b959ebb807a660ed676a2abef37c", "guid": "bfdfe7dc352907fc980b868725387e982a8e5565a4cd0e92fa2ea0f8376e64a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985566fd9c6ab1b28ccbe7ed1f43bd6d83", "guid": "bfdfe7dc352907fc980b868725387e988075cc4408d91e97e24ce8859ad2fb94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff644642964443a4b4dc15d1880b4ce9", "guid": "bfdfe7dc352907fc980b868725387e986b1f936731fdb0b1c19202f4c14cf1b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8d239edb344f709c5147e9c4df34d8b", "guid": "bfdfe7dc352907fc980b868725387e983450a797f6cbc9b2a20e92fbd9ed8023"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d67163d80ce9005ca74bf153113d1e1", "guid": "bfdfe7dc352907fc980b868725387e9882be7300bf40a87fe77eb695012a3c88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0f65a8b6515beb265a31ee2a3baef04", "guid": "bfdfe7dc352907fc980b868725387e9870d4ea14dc8c0c4be9188b41ccff1dda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd298eee93ca41fe91e947ebf75513d2", "guid": "bfdfe7dc352907fc980b868725387e980101aa78b5c70e0637b4c3101d6a341f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff072115bd835211697f8bb346a36a46", "guid": "bfdfe7dc352907fc980b868725387e9804a58049772a4f76086d01e8409fb2d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7ba8642a848095c534f4b49caaf7f65", "guid": "bfdfe7dc352907fc980b868725387e982bc565e4709f54431a48eaf7960248aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981387396340788877d362befebb209976", "guid": "bfdfe7dc352907fc980b868725387e98630967c3c78813dd1407e39cbfef7877"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845d57982e889567c7addc6458862d6f4", "guid": "bfdfe7dc352907fc980b868725387e984d19299b7720c780f3281a37d090bde8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982716d92d89a589cb1b28c306de8c5a3a", "guid": "bfdfe7dc352907fc980b868725387e9816786dc4756cac957daf5f36435e4234"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe5f7483af95d4207ac347ca6d8c903f", "guid": "bfdfe7dc352907fc980b868725387e9834c73fb4480d3a029f6d8d080b3ba0c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec4eed1612f39d989fb6c8b2ddc07e03", "guid": "bfdfe7dc352907fc980b868725387e985c3aec608590487b30b825486b58f92d"}], "guid": "bfdfe7dc352907fc980b868725387e980e098c889f91fa139316ac7af7117a84", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e9872f659c7083776ec290658bdc7f8cf3b"}], "guid": "bfdfe7dc352907fc980b868725387e986ec697a84bcff16b8ff2a9cbcfaf2f74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e933d1d755bd6901a16731ace2d7ada9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}