{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9803db1dec11dcfe6197f595a84cd05f89", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6da72224a0002e2c8d4ad6a4140a101", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98858c351fecdeb0c2470462d61c10dcc7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cab3693a69eb1c553c791758183685f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98858c351fecdeb0c2470462d61c10dcc7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f480631d57f584ae82569893436b3d19", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988a94ce9612a49533117da9476c51c60e", "guid": "bfdfe7dc352907fc980b868725387e98faa2d77c0ef7eb87f7f8421e51c1b40a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e3dac75ef5081293a3cce12f493338", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cdcf26dd7ab10053d579e6a131abb8fc", "guid": "bfdfe7dc352907fc980b868725387e98409fd3aace548c9618c77b80aae677c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98465bf7e419de81c45b7316aa9919ee26", "guid": "bfdfe7dc352907fc980b868725387e985196a13d2cbbc862445af5b2cb03a1f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b793b882d103e9cd3446fed035d5ae37", "guid": "bfdfe7dc352907fc980b868725387e987685d2fb6e4a18ffcbe9dc6d2b132ef5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98342f079aaf48c7f8824eb749e51d2840", "guid": "bfdfe7dc352907fc980b868725387e985faa0a6afd9152a702279f886709658c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823dbac3a17369004836c7b5d89aad66c", "guid": "bfdfe7dc352907fc980b868725387e982ce541bed1416e89638a60c7acdc37e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be18067c734e3bcc37278ed2c192b0c7", "guid": "bfdfe7dc352907fc980b868725387e98f15ce06de50e5f7e2eee6b3e60a1274c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887bf822af8df1dff914f4744a268945c", "guid": "bfdfe7dc352907fc980b868725387e9868130c53a26a9bd54a7f8b7784b27f74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838c5b0f2d65383cb54752dbfe8de5fa2", "guid": "bfdfe7dc352907fc980b868725387e9868358b66d7cf8ed7b4720a00f8b7cdaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98597feea249b406bc2627715a21afff63", "guid": "bfdfe7dc352907fc980b868725387e98964084f7e6ec71a7a7b4cceec4fa601c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a406964034ef365569e9bf1a3141ddcb", "guid": "bfdfe7dc352907fc980b868725387e98d9344dac74ad106de5b9b132745361de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897ab2571a49bc16b2b27f7b3f24d07f3", "guid": "bfdfe7dc352907fc980b868725387e987a9a295625e6f5ac79ba8a0720ad98ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861e4856d860f9d8282ee32d12d92fd13", "guid": "bfdfe7dc352907fc980b868725387e9859f10860189369b5f2e91d9022950aa2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985752c45a5683b2565f758b36ee9e6b23", "guid": "bfdfe7dc352907fc980b868725387e9850c31715f13256c8f776fc8cf9f2f936"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd2736a3969137499cd515fff20be746", "guid": "bfdfe7dc352907fc980b868725387e989d57df143c0383715e0f92de2f5d386b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d91d3118ff234bbb75ad05d818645ae", "guid": "bfdfe7dc352907fc980b868725387e9827c5c47e21590024b0febabe1607abf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98031efdeab299ed1cf3b6370697e1488d", "guid": "bfdfe7dc352907fc980b868725387e980a0adbf36eb92b45afc19d9f8280720e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dcc549d89d42d977242749a371238a0", "guid": "bfdfe7dc352907fc980b868725387e98fd0bc0f379a9f116ddc6cdae764ff128"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a5838b927d122884b515ae511856a9e", "guid": "bfdfe7dc352907fc980b868725387e983502dfc133a17e0989d2b26e841c3214"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98209e4b0038606d348734bec1f63da145", "guid": "bfdfe7dc352907fc980b868725387e9845815866d49bf7e1f5a23ee326b27f74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c04f279ec85d924fd8ed09c545eccf8d", "guid": "bfdfe7dc352907fc980b868725387e98a8652d4512a0fcb0f24f1ec1ee24b97b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae5d177787789bb7dc9d805b042b0a73", "guid": "bfdfe7dc352907fc980b868725387e98f79d8f72b5c08c73b8e1ac89f506d924"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899139bd665dac45798699816042fc3be", "guid": "bfdfe7dc352907fc980b868725387e98a175ebf56414288b8273df9e6f32cc15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98852bd62d46b6081498e208fc831632fa", "guid": "bfdfe7dc352907fc980b868725387e98af29c593b13e06e27af2991e7170bbb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ddc5df2e8aa103efaab2e9d8f7fea8c", "guid": "bfdfe7dc352907fc980b868725387e98f39bbc91cbaf4a1d851ee4124fbeb247"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878c36a5eb49df69078eac53cbb3de768", "guid": "bfdfe7dc352907fc980b868725387e9887e8d831229980264eca2bc6c4a15d31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e398892af7028fe62bcbbf22b9c15dfa", "guid": "bfdfe7dc352907fc980b868725387e98d522377a84582c4a27bd0f23238a1d13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b7445ac9b8bb04942f88160542d8bde", "guid": "bfdfe7dc352907fc980b868725387e981632bc241e1ad337e53f2c7b94301aff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982193e23011df78d14a063a6cebea67f3", "guid": "bfdfe7dc352907fc980b868725387e983e100b2a8d1ca327e8898c3fecded5ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bf050f15212e042e3d775ec9bb96bec", "guid": "bfdfe7dc352907fc980b868725387e98ef1751b75a1cbcc81996d66a5148bb92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838c77ca6c8d962a1997cbd60615f6462", "guid": "bfdfe7dc352907fc980b868725387e9816dc5320714467a118cf831ce4f2fe3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6893766f7f22d7632dbb7ce8d6dd248", "guid": "bfdfe7dc352907fc980b868725387e9868d44645d337fc18e4934144e69a155c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888ceac5dff41033782e4e0ce0b663f7a", "guid": "bfdfe7dc352907fc980b868725387e98be3f0ea2723f07116d8c48d487f80085"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbc593ae200e4716a563deaaa6bbf4d1", "guid": "bfdfe7dc352907fc980b868725387e98f9fd0660da7c6325707eba230b8e1873"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893166f74df70b692f64f7b2f8f65c984", "guid": "bfdfe7dc352907fc980b868725387e98a68c47fd64021a85e003cfdf67ab363a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98307e0c0a9dae0f4878099de5f8a4c211", "guid": "bfdfe7dc352907fc980b868725387e981726a085fc70f337fb31ddb40a179200"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef8811e1b01d378a50a458b5b42a0a4d", "guid": "bfdfe7dc352907fc980b868725387e98204b5818ce98e8eb7842231da020a55a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810243bce719eeeeb6b5bd13da42df6aa", "guid": "bfdfe7dc352907fc980b868725387e98b0606117704289c12d09ae1c9e6c1c79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896397c2b7e9bdce56de65bd4922b77a9", "guid": "bfdfe7dc352907fc980b868725387e985e053a95f4512dffed0117299c43ee3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b59008fb2c1fef70b449961005c5d13a", "guid": "bfdfe7dc352907fc980b868725387e98d259083229b4eed28de4a364a94222ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bcf959ba4a7a6136809bfa7fa0d4d15", "guid": "bfdfe7dc352907fc980b868725387e980932ac07df4cbc9b765314c156a15486"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811ee1bb3da6f167a81d6c755be481a9e", "guid": "bfdfe7dc352907fc980b868725387e98697c834ffbfcd2e56982c78c09bbbfb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d553e5b6c070211497b53dc6cbcef6b", "guid": "bfdfe7dc352907fc980b868725387e9861842675f4aaa63ce966ab7cb03166b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98115be52996979be5f21d81fd74716fe3", "guid": "bfdfe7dc352907fc980b868725387e98ef0e8e212cd09a19f3bd8b4b55dcc72c"}], "guid": "bfdfe7dc352907fc980b868725387e980055fbf3a11bc15129d28ebbe5cc1480", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e987ead9e15fc09a570554fa963eed1137d"}], "guid": "bfdfe7dc352907fc980b868725387e98da9cd099e7190758cfc42156c0f4e548", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8defae1b39d33d1c1b6a7ec3e8cf51c", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e987fd8694c1d36b88c99f78feb0d04dd25", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}