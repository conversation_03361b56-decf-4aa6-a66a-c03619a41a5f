{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd55ca60d3b3a49527bddec6fcce29a7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d61d7a67ee3c6d3d37380f3f51ee3cb4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fad688ba164b304cf26a67312ffd82c6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dab62f119ddc6e04ab6af08f3b3b4064", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fad688ba164b304cf26a67312ffd82c6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982e6f5553141290d531ac4eb68f674c0a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984ae694cb006335a3532c0af746d37530", "guid": "bfdfe7dc352907fc980b868725387e98a72b349186e455cce648beb671cbeab0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98697df91646c3d23a59fdee8889fd278b", "guid": "bfdfe7dc352907fc980b868725387e98f8608e623cfb23d975f08dd44c3abd59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d61ff3e6e94caec3dc5e5bb0549c8e73", "guid": "bfdfe7dc352907fc980b868725387e989dd7f924098856644608682885321e0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956a7d7e2d87288a525c399104bd1456", "guid": "bfdfe7dc352907fc980b868725387e989f5acb56612a37c85e027dd8a49b00b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb8731e2243af33337ce966478fd0a3c", "guid": "bfdfe7dc352907fc980b868725387e986c9db43fdf58bcfaeb0064ee09aef6a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7883bfdd23e3f3feee8a2cf3f48a654", "guid": "bfdfe7dc352907fc980b868725387e98578044e0a4f9e812597a17f18262eb9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b73afd79fe3dc07c80ea8be08f8ce86e", "guid": "bfdfe7dc352907fc980b868725387e98f7a89544c9b03a8f92e7e61939b27cf5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98337c700e257e90852ebd5cccc422be08", "guid": "bfdfe7dc352907fc980b868725387e980a9d9002f6f429d6a5c5bdf1e7ea09e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbb228b2898d06a8b6c98ff169364a32", "guid": "bfdfe7dc352907fc980b868725387e98609e3d9ae629fc45c5e88885d93f9e16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981412265679de7f63f18e35cd120d7b82", "guid": "bfdfe7dc352907fc980b868725387e98313875e1bc3a83aa58a4331b63b46d93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984883a574b87ddffe129911c1d65a93bb", "guid": "bfdfe7dc352907fc980b868725387e98a7846a6db37ea4b93b0eb0029b32fafc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859a0171e6103ddc9184c5d62f575a09c", "guid": "bfdfe7dc352907fc980b868725387e986e8053e869e825c72cd62b27c6c6fd1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc3f05adde94adc41c5940d5e6dec85c", "guid": "bfdfe7dc352907fc980b868725387e98f37be972aa9b589158b3034cee96430e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98803012e5b947497ff589a3fa6e3e9941", "guid": "bfdfe7dc352907fc980b868725387e984e0381bed7116388cea991e0aee16507"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98932eff5d3a61791e873902f297ff6314", "guid": "bfdfe7dc352907fc980b868725387e98fc97c99dabd31fe5985b8d8df6b3f33b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f71bb1d7c5dd31665a578b9e46cd3e05", "guid": "bfdfe7dc352907fc980b868725387e98c38f7d861d0b3032d99d6f2067e51dc0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98516e39054acc2b380b15e7a15ae63ae3", "guid": "bfdfe7dc352907fc980b868725387e985c9510a946f14dea920485d0094f82d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5d8dff5334d9146fa592524073e42d1", "guid": "bfdfe7dc352907fc980b868725387e98125edc5a495bfd7e63acafdfaff84e60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895ea8c6b5664c4926876783d76392836", "guid": "bfdfe7dc352907fc980b868725387e9882ca00574ca6cdcbf37ee8c8e05e6695"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854ef21eaf66a1dd7884a566b534e18bf", "guid": "bfdfe7dc352907fc980b868725387e98e179ed52cde0d462f0db8e6f0bf90aed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f0c9e0bd3cd78ff483b640c98520540", "guid": "bfdfe7dc352907fc980b868725387e98f8b88df170c54bfc84550e1640323e19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848f68a04f41d169badf78c32fad5b51a", "guid": "bfdfe7dc352907fc980b868725387e987b6eb7494e5be7630298fb66b4b355b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf528c8ca304fdfbb11811954971ca50", "guid": "bfdfe7dc352907fc980b868725387e98a051bf0cf58b214845a24578e62af179"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b77d87f4f72a12dcac46f16f2c526471", "guid": "bfdfe7dc352907fc980b868725387e981223b17a0a3497307fc262e302eaecbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f32138f4cf98e79badcadcd0387c8cca", "guid": "bfdfe7dc352907fc980b868725387e98b906b67d59b2e3332bdcf4458855734e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981336f1e2f761c57397e317790c783488", "guid": "bfdfe7dc352907fc980b868725387e9877b4740f6cfa649cbe005e07f5d7b3ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98362a064c8c9976755b80147f438d111f", "guid": "bfdfe7dc352907fc980b868725387e983269ea556059bed73dc0523fabda95a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f74fe18df53053db23c16304614f6a", "guid": "bfdfe7dc352907fc980b868725387e9898fe88c540d351cf06c40270d784eb2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af5d4dc3a1423201c728123b9d994d1b", "guid": "bfdfe7dc352907fc980b868725387e982d07d873b8c4f4d31b4892d650a6d320"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832e875964c84512a4f45a1be4f787da6", "guid": "bfdfe7dc352907fc980b868725387e9887a59f9187e8803a551f332f170715ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e83cc3f9305be8ee7b3ee685744f0d05", "guid": "bfdfe7dc352907fc980b868725387e982e27ab4e0b2da22bfcdaef740c895cf6"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f8dab570bc138cbcfccec78fc3a31c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cc21ec040c4f193b0945f361185655f1", "guid": "bfdfe7dc352907fc980b868725387e98397d25fcb51672a84717a3eb38759ffc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3021e9c06722ac4156f817b78c137c1", "guid": "bfdfe7dc352907fc980b868725387e98470d2a4f0092bf9b94db65d8675b0177"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f423ab2e8a1c7a9344fadf6f44bb9c4f", "guid": "bfdfe7dc352907fc980b868725387e984f2449e2969c7bebf6185f97442d4a5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cde1d526e0b64ad9806d301dbc33032", "guid": "bfdfe7dc352907fc980b868725387e9805facde10efcd4f95c5bb576d0efa3b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d4b4b1055225d6013adbe883cff8963", "guid": "bfdfe7dc352907fc980b868725387e98d9240b636cefb865b8e46b2795e4dc11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c56977262cdf5192a1d67df0f22f9c68", "guid": "bfdfe7dc352907fc980b868725387e983c0f7ac9b7b4541aff741b1431569047"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988443373585e62540cb1bbee472b84144", "guid": "bfdfe7dc352907fc980b868725387e98e0d6bf1d16e16f14a7982bdb1c6eccb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc9677b42584ec67352262e6b362056b", "guid": "bfdfe7dc352907fc980b868725387e9816bcffce5ec7e18e5f8d3642095233ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983884d0e681200941b18de8f9b10c8a98", "guid": "bfdfe7dc352907fc980b868725387e981bcdcd6a97acea42a194f55a54e37896"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9ca31ba4afe2b6d7267d460be17f10b", "guid": "bfdfe7dc352907fc980b868725387e98eee7215264771c953ce678dd6f171711"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98137daa956eb5c4d77395c62194396a8a", "guid": "bfdfe7dc352907fc980b868725387e989a623b2af4410439a5721fc23caac190"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98453bbdac454047b68835fcaa1af905c3", "guid": "bfdfe7dc352907fc980b868725387e98dd5948585d48b632b89544e4a1a50d16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d02c3af8312d49ef049261206c9ebf1", "guid": "bfdfe7dc352907fc980b868725387e987737701dcd3ca2020c15ba8012a30675"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f215764428b92621acb5698b43ef76e", "guid": "bfdfe7dc352907fc980b868725387e98af490dde15f68fd71154b97bddd70705"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2db855a7711995527c7dc313706bce9", "guid": "bfdfe7dc352907fc980b868725387e9815a25d4b03173657d8f9d64fe1f6b27e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1d0d2ec3b7dc5984e31bfbc491da436", "guid": "bfdfe7dc352907fc980b868725387e981fe33e21315d3c56a36927c5a37eb8ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98276f0b75c38fdbfc8828af7b53060ad5", "guid": "bfdfe7dc352907fc980b868725387e989b1f8cfd0b7e5f94b32e4d529cb192fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871b1d129d685c828edac98882e91e9c3", "guid": "bfdfe7dc352907fc980b868725387e98cad1d065f4f3b4bac235edd67ae3b2c8"}], "guid": "bfdfe7dc352907fc980b868725387e9882d56aab7c4174595faf0e65e7e73cc2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e983ee7dba3fa132afba46e4a5d476d1f76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d05a97faeb775352b784fcd3a04da5fd", "guid": "bfdfe7dc352907fc980b868725387e980a3efed643f456933bbe9801a6d99148"}], "guid": "bfdfe7dc352907fc980b868725387e984821aff17b93e72fda39e8597a8f2256", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9890c0c848c14c40aaab1331899f76fd52", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e988774ffa4defc9b2baeea864942420939", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}