{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f3c5bb8a19ed83f11877dc7695b72173", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/the_apple_sign_in/the_apple_sign_in-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/the_apple_sign_in/the_apple_sign_in-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/the_apple_sign_in/the_apple_sign_in.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "the_apple_sign_in", "PRODUCT_NAME": "the_apple_sign_in", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987d9ac8fc471cbfa1104e1aea73de4c5a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f36066ed2828b77b63bef07585e73eed", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/the_apple_sign_in/the_apple_sign_in-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/the_apple_sign_in/the_apple_sign_in-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/the_apple_sign_in/the_apple_sign_in.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "the_apple_sign_in", "PRODUCT_NAME": "the_apple_sign_in", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b9f12e28dcc578e363646641cf460ab8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f36066ed2828b77b63bef07585e73eed", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/the_apple_sign_in/the_apple_sign_in-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/the_apple_sign_in/the_apple_sign_in-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/the_apple_sign_in/the_apple_sign_in.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "the_apple_sign_in", "PRODUCT_NAME": "the_apple_sign_in", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ecd80b8ab94c9bebb6336d495e731b17", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cee37791108962507c8f02973f4b5dd2", "guid": "bfdfe7dc352907fc980b868725387e98b4037b37963f617930fb9018310b393b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f090dae11870043648dbaf8fc3362e2e", "guid": "bfdfe7dc352907fc980b868725387e98d5af078acd978fdff99dc66cad07e6d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bdf53ec0bbe24ea2227069ff561d331", "guid": "bfdfe7dc352907fc980b868725387e9876c8c6cbad606ced24cba0a9a88b4ad9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986333de95e855de895ae975d227d63e82", "guid": "bfdfe7dc352907fc980b868725387e98367fb7fcd6ad6acabdaba6d352a836a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad576b8fdab4da9853b97fc8231def9b", "guid": "bfdfe7dc352907fc980b868725387e98fe9af0f5907d6e4e05735b69ed86ab7d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98422fc62ad85f5b3274441247b0370b88", "guid": "bfdfe7dc352907fc980b868725387e98de34aa0ff35b6ac4a30b38cbd90a98ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c43d239d0a3b6e5243bcea0f7a88bf09", "guid": "bfdfe7dc352907fc980b868725387e98e7d3422cf3aa3aa2fa3a5edce0db799c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98917b6434793ff547ed3f2a1b7fade8f8", "guid": "bfdfe7dc352907fc980b868725387e98e8f7cc292484ceff0927d9ff2d296e04", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983d8825c2773724cfb523861f400e1cdc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a35349224b5b8424f0e38a1f31d3da3e", "guid": "bfdfe7dc352907fc980b868725387e9849274907a35f71cd462160545b633eac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983457ef883be9cc821bc1f87c82b96c01", "guid": "bfdfe7dc352907fc980b868725387e98f50225f355ff1352ef270fb20e37a017"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98016249ae99ab2c05ed3bb7b3bb0b8d07", "guid": "bfdfe7dc352907fc980b868725387e985385c0564ada2b841ec816ca91a194d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fde5cf7951586bda56c51ca7ca57cc03", "guid": "bfdfe7dc352907fc980b868725387e9870d3fbdd27e817828c318e28fcf99a9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d406cdab67516fb71a0f8393476fae93", "guid": "bfdfe7dc352907fc980b868725387e9874fbbad1dd401f45ee8811f43363125c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981826727bd6de9b0398b077566e6d8ad7", "guid": "bfdfe7dc352907fc980b868725387e98ad7cdfac6101c9f6bd39710f33c0dc38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819647877b58d7370a17fcec4479e6af7", "guid": "bfdfe7dc352907fc980b868725387e98877e2920644cfe4529df695ed56029ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ad739ca488c526cb6cabbcd82c44b56", "guid": "bfdfe7dc352907fc980b868725387e982d191b1a65b5d95b5102d762175a7f28"}], "guid": "bfdfe7dc352907fc980b868725387e9866b1b2d4141c49bc737c39039ee3680d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e98cd28bcc0e2d670eaa377b09c98314ef4"}], "guid": "bfdfe7dc352907fc980b868725387e9838bfd78945e9d0e62e78555039890b62", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9813442716f2a94b58adb48664b90c1942", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e989d2bb4eba30d5299dd3a5a1d7d0c6a37", "name": "the_apple_sign_in", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab4be80cfccd1612f44b2d4a413cb1ab", "name": "the_apple_sign_in.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}