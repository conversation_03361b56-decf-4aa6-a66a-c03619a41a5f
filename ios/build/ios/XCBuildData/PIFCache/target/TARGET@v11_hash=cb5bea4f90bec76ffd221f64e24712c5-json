{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c024201135515de17018a7eaf21380b3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98398bac938fb47b84c16f19633c15c92f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ddc4d87607417a8d4418a2ea29b5d4f9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839d79a52f24f54321de12379511903ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ddc4d87607417a8d4418a2ea29b5d4f9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894fef8acb7de742f96aaab49fd64da62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987050e3d9bea0d3416f510a29c55faefe", "guid": "bfdfe7dc352907fc980b868725387e989bc1a24dded92711f33d03b62ff58c52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845d2ca69d73206d445627dadfd4d35a0", "guid": "bfdfe7dc352907fc980b868725387e98b54d34090f71de68cbbdf4bf744d0453", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed328f7ea86f5eea8c6da234615ecb07", "guid": "bfdfe7dc352907fc980b868725387e98e33ba375f8cd98aac21ed2eb2c8f8ff1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983617fbe7b7280c15d32ba68a69b61481", "guid": "bfdfe7dc352907fc980b868725387e984617eba0e7790d75932eadbe142593d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8adcc96ac90f4dc88ade6e4840e5bd7", "guid": "bfdfe7dc352907fc980b868725387e9874103f68c913ad4b6ce6324c79d12b2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cb0096f9555da16ec1216a1698ba828", "guid": "bfdfe7dc352907fc980b868725387e9814aa089d13d473fb05ac7737732494fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98519d9cae64d5fb627ea9b12b3343b967", "guid": "bfdfe7dc352907fc980b868725387e98d82d193dcb38b0a60f91f8855c1cba1c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c43c4ec9668de56dff448d5406f489b2", "guid": "bfdfe7dc352907fc980b868725387e98abfc8d15da2c3ba2b15222259ef0a5f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d93018524c81b7d7f04b7372ccd07302", "guid": "bfdfe7dc352907fc980b868725387e9852aa86f6bf4b93e152cfe17cd4658e42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef3d73bc76f58ca5633d323afc66b0b7", "guid": "bfdfe7dc352907fc980b868725387e98d9731a5bf70f1b437081b0c75ee70175", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c394b52d90c23fc2373c8978e4f79c0", "guid": "bfdfe7dc352907fc980b868725387e98103d3b5e109b952dc1c6da9533cfeb9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f93783cfd9b61bed70af0e7f273ca9e7", "guid": "bfdfe7dc352907fc980b868725387e9803a5974ed79de359510a8b3433523704", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff28e062ab2758c9cae512b464f3abff", "guid": "bfdfe7dc352907fc980b868725387e9808802dc129d87e5242d042e967997913", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855688c48a92e389054d12f150cb7b4cf", "guid": "bfdfe7dc352907fc980b868725387e987d67c59640fd8696befcda5f2d58ddd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2dc5fa827d89c5fad0911d930269837", "guid": "bfdfe7dc352907fc980b868725387e989314e081c3f5bfab1279e3c2c1139a24", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983145ad27ab9a5d703e29269b9b2b583a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985824e8bfcba57c9a377bedce3f0ff04e", "guid": "bfdfe7dc352907fc980b868725387e984e7674544ca7c886246319462d18af5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866ef30617859be44d2351650d7e47690", "guid": "bfdfe7dc352907fc980b868725387e9885055f98a4f54acd2266bbcef0d5c523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc57e50cbc2d2345074dc556effb60a0", "guid": "bfdfe7dc352907fc980b868725387e981128375b80e7dc85c50ad37ba3a1df1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b54a10c8d4f604cc481a4bc3dca841c5", "guid": "bfdfe7dc352907fc980b868725387e98839fdc157e92bfe23f7fccdadfe6e9ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e24fd2ef6b24c08696868eaa31db315", "guid": "bfdfe7dc352907fc980b868725387e988c9aed005a84b3255660bb31050d6733"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98851406fe5fbaf828e09405610a850c94", "guid": "bfdfe7dc352907fc980b868725387e9837fe325f8e1b76afaf8e327bef6b052d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804cfc900b3c481b4393b82a81f828517", "guid": "bfdfe7dc352907fc980b868725387e9857d92b99c332f55560aba71f94cbf7bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817d3bce87c6a029a4ba34f5115384555", "guid": "bfdfe7dc352907fc980b868725387e98df57938d4f70167da2c61051332da791"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e730f8df8b95cc8ffced917485b342d", "guid": "bfdfe7dc352907fc980b868725387e98265c82e573935ef4b8419b72ab96a9ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bb21ed6fc879ae3b6defc8d5c60928b", "guid": "bfdfe7dc352907fc980b868725387e98678ab59e7f607dd186118ba5fc4ba5ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985932715e589b9c1ba19303942fb436f2", "guid": "bfdfe7dc352907fc980b868725387e98a89106363ecf1c82c77a5e97d941ae89"}], "guid": "bfdfe7dc352907fc980b868725387e9884539da3bdf2ed4ad426ca202aaf6bc7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e98c1dae7e29df157eddda815ae5462108c"}], "guid": "bfdfe7dc352907fc980b868725387e98119e723bd2257d68f10fcea70feb1342", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981c10b7a3ff26395a834064a0172d6124", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98a3435bfc3b1be550d78f0653eca9c848", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}