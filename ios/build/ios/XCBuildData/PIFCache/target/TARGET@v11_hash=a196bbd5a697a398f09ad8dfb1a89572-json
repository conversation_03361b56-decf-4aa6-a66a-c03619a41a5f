{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9f53d4d424d99a1451f0dac71fcbb4f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Alamofire/Alamofire-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Alamofire/Alamofire-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Alamofire/Alamofire.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Alamofire", "PRODUCT_NAME": "Alamofire", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98121b0755a8b62b0b8ed71a6e8b8f6672", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c720914850ac40012476afdabb0adcd2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Alamofire/Alamofire-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Alamofire/Alamofire-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Alamofire/Alamofire.modulemap", "PRODUCT_MODULE_NAME": "Alamofire", "PRODUCT_NAME": "Alamofire", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981c6a68d54485c4154207d181c238992a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c720914850ac40012476afdabb0adcd2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Alamofire/Alamofire-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Alamofire/Alamofire-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Alamofire/Alamofire.modulemap", "PRODUCT_MODULE_NAME": "Alamofire", "PRODUCT_NAME": "Alamofire", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98482ad45bb9aadff03128cfb2d6392b79", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c2553a40ee63432677eaf82883ec7d4", "guid": "bfdfe7dc352907fc980b868725387e98eabf857b26ef976742b01ee3c376981f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982714de58869b100548f19e60899359f1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9870bcea12884c50c8d089e85ec16607cf", "guid": "bfdfe7dc352907fc980b868725387e985b686ed4d6dac509af5b9b94214a96c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888301ff6a079516c20128a9810c78250", "guid": "bfdfe7dc352907fc980b868725387e9801f26390bfdb9c87ac541e1d55423a0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baf7da8ea37a82a86ac0fe9500575e76", "guid": "bfdfe7dc352907fc980b868725387e983d28b9f0db0234b5e5700a485e273710"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d1201c4d525c8869055aff687fc86db", "guid": "bfdfe7dc352907fc980b868725387e983350a3f0f85300fe64b83e7c4a0878cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3aa24b43f828e67376f0249f10d4a0e", "guid": "bfdfe7dc352907fc980b868725387e98941ec65b65566af2b941c9816e9f0a6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbbf729544952fa620a253852cdaa28d", "guid": "bfdfe7dc352907fc980b868725387e98a1b2c8266eb21831ea28e0c4e67cf9a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0de9f6ea1bf51b8e36b02109eb3199b", "guid": "bfdfe7dc352907fc980b868725387e9854370bcdde1de474af1824e6edcc877f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983da796e2228f172e126f66e186260cbd", "guid": "bfdfe7dc352907fc980b868725387e9810bf31f402db2e307a62693acd3fff11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831f5e84e3e75f782dde49328707b406c", "guid": "bfdfe7dc352907fc980b868725387e984cb35c76a0b102f6cbfd03415421f2cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e172c818c53c5cfc6c0a0c2c1956c386", "guid": "bfdfe7dc352907fc980b868725387e98c0eae51003d7ce609068908811952f58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823b54a6904c8d4c0a32a1b5d0b37f338", "guid": "bfdfe7dc352907fc980b868725387e98f5a8cf3fce24ca5890f537a6bf478285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869a3993533fca6549f11cc5e2877ab56", "guid": "bfdfe7dc352907fc980b868725387e98969a05a163becc67e67559abeba1609d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f787c6fff289d3f3f7a99c41fee0bff", "guid": "bfdfe7dc352907fc980b868725387e98a940a52b6c0a1ad081bf34d8906cb5cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d9d6a7e4ed15153a8eea48fec0920f5", "guid": "bfdfe7dc352907fc980b868725387e98312294a959af53815804f6b3a2ec9883"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1f89a3e7ec6a8ce0622b660a74a8885", "guid": "bfdfe7dc352907fc980b868725387e98b21f1a4a0262108a71848bb6abe105bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c77643ac699b77371d8bc0b619bdd5d8", "guid": "bfdfe7dc352907fc980b868725387e98bd465a96267ec176021587b8c203c8c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f029742ea48069fbdc0c223d9bd4997", "guid": "bfdfe7dc352907fc980b868725387e987fa28c047f5ba9909e474571038f4ec9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ae23cb6a10af11ed4d4f62280c7a8f7", "guid": "bfdfe7dc352907fc980b868725387e98653be64ec168091a3ac8937767467cdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c6bbe152d5b7a0d0512110260288020", "guid": "bfdfe7dc352907fc980b868725387e98e84a9a2d9e9fafc59517ae0c35d89337"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b675eca7a17e56bd3733def91f982de", "guid": "bfdfe7dc352907fc980b868725387e987c70b9566b553fea2f0f8d462915120b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6e2f1f179eef225260fa02b8cf98f47", "guid": "bfdfe7dc352907fc980b868725387e98069d62851ac1903c5642745f9716be81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e819a34ee922b792c0204ed00d2db41", "guid": "bfdfe7dc352907fc980b868725387e987283aa78c0203ea06bc90dd657f15b64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee66012d9dc4044b52ece2a7ef3db64d", "guid": "bfdfe7dc352907fc980b868725387e98e7dfe2777851053df0dbf2479dcd7b43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831431cbebce91ea050769a674b89e03d", "guid": "bfdfe7dc352907fc980b868725387e98481a9a0d780377efa6643e6d0229b180"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3622bd941ce5480c7c840a9f38a1752", "guid": "bfdfe7dc352907fc980b868725387e988cf474d1416f27b2ab76d9c5b3a10890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98017298061539359ebf179f74e03f39a3", "guid": "bfdfe7dc352907fc980b868725387e98d909467045a5ef04dc3d31660ecb880b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895e36ae6d21b9ac0e6ab840ddd2e2767", "guid": "bfdfe7dc352907fc980b868725387e983bc577800c5c2611e27b456e3aee93c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857ced45da6f604613d1d030b59d8d602", "guid": "bfdfe7dc352907fc980b868725387e989881738c86a0e2dccbaad476742c01bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833634c2bdfe00c34d09e76cd78381280", "guid": "bfdfe7dc352907fc980b868725387e98391529c8998c51bfd0606eeaca1a6a46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852deb5c62883a7c9d84e2184f7df9522", "guid": "bfdfe7dc352907fc980b868725387e9868163c93bc26b33b80f86d32b0c3ec1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3fc678d8852e7e491d31a414d6636be", "guid": "bfdfe7dc352907fc980b868725387e98f5bf4df0961ca66dbfd21b1af236c0fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d113131c14c36f4acc6ee2bb547d1cf", "guid": "bfdfe7dc352907fc980b868725387e980e7fab2bcbe3387d38d63313656d1a69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcd80ac08fab2a2288f6e84f87e960f4", "guid": "bfdfe7dc352907fc980b868725387e98590d8e35aa1fd2b1bbefecb8b9cc2ef5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851fad827e96113696838f4906c037774", "guid": "bfdfe7dc352907fc980b868725387e98d3a6767af2e0439af8de06ce7c82795c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfd61f78c656f39c38b51fe3b2fc3203", "guid": "bfdfe7dc352907fc980b868725387e9818e980cafd758392e5a0f2798d510740"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efa8aae1be3145dc826b60e7c9b950db", "guid": "bfdfe7dc352907fc980b868725387e98108fdf4e9ee307e4ad6605c8feb30cc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faa8995d15c4ad3d21ad25c6cd6ce185", "guid": "bfdfe7dc352907fc980b868725387e9804a4a476ecf801ffbc03ae8d680d5847"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d34a16333555d4494d8fa2f5e65d704", "guid": "bfdfe7dc352907fc980b868725387e98288bdd8aba74c16339dfd13bb05474bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efeca3dc8492110157e3b5b1ade423f9", "guid": "bfdfe7dc352907fc980b868725387e9883fc5317a05af86fd8924cf1bfc2a682"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5a1bc652104816d488a87ec7ae1c244", "guid": "bfdfe7dc352907fc980b868725387e983a03a99a0f7588db0594eab2fe66191d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98463bd876e0e4c58453321012830d7e13", "guid": "bfdfe7dc352907fc980b868725387e98829d47dbe33611e157c204cd4f362ca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808093236efa889c621e929e07ef39f7e", "guid": "bfdfe7dc352907fc980b868725387e98b27640b9bb5b87601cfd38838681e8e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caf04cd6a23c7fbf6f28f817acd8c871", "guid": "bfdfe7dc352907fc980b868725387e982abde40c6fa8606ad0c823b6cecb8105"}], "guid": "bfdfe7dc352907fc980b868725387e986738216bf3432740b8b6261eb86e6a7f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d37e767dece2e0d83baf7f4cd4cc0672", "guid": "bfdfe7dc352907fc980b868725387e982f12b8fd0303269b0a973eebc22ae634"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e98b64abcfa4502efbeed9941b9f08dbc31"}], "guid": "bfdfe7dc352907fc980b868725387e9828ef15012b09ad33035a1c4da4bd8f72", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fc76c8907ab85411f2ad32e447c1fb58", "targetReference": "bfdfe7dc352907fc980b868725387e98c0cf7d8ee0c03c9d9476c5a72bca59ee"}], "guid": "bfdfe7dc352907fc980b868725387e983ab21375c7ea070327155cd87e1cf0fa", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98c0cf7d8ee0c03c9d9476c5a72bca59ee", "name": "Alamofire-Alamofire"}], "guid": "bfdfe7dc352907fc980b868725387e98ba3679b4c4428e6a6d83c5308d3af99b", "name": "Alamofire", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9878a7912f930d33f69a5abbeb079e5b03", "name": "Alamofire.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}