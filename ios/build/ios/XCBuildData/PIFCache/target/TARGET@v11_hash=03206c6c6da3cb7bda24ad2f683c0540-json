{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b5506d7e42253c31527eeac20344da0f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a730ea744191d501dbb103477d99b10d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d0f8eff312f445b0e0c81067a2222e0d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fc3198640d3c0bcb021bdc2e3c7f8b70", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d0f8eff312f445b0e0c81067a2222e0d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987a464a103475e8b2e921298263ec70ef", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989dc9e93bcfd9938f762442e34a888f58", "guid": "bfdfe7dc352907fc980b868725387e9863ec2f35879af34d741d24cc171349b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4e485dbb91c33e16026a581527ecf47", "guid": "bfdfe7dc352907fc980b868725387e989f4e7242ada2a578819b02656ede5e0f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5e4abe088b891f20488b2cf203f211c", "guid": "bfdfe7dc352907fc980b868725387e98b686b2d956c650f1c945d4d0356c1e4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e459a3568b8ad6edcfabe8f56c465af", "guid": "bfdfe7dc352907fc980b868725387e9843c8c10f8b70fc7f303f8bb9fe5fdc2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981814d8f863343789da9bdb1477dbd327", "guid": "bfdfe7dc352907fc980b868725387e980a94271333ae7540189e355704f81bc7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd59635b8e47e3c82c0c1205fc8260b4", "guid": "bfdfe7dc352907fc980b868725387e98fb91704b1eaf4659cef8d7126a5fbf70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800a6d72d7d17250ee806337f7c00343a", "guid": "bfdfe7dc352907fc980b868725387e98a7ee17686605bd270f560a1fae8be8c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c3552d64aed37abb161517b705417a6", "guid": "bfdfe7dc352907fc980b868725387e98db0a1e31a6ef7a18ba7957da7f584adf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827d571ca5af5a9c5a4697d645db80852", "guid": "bfdfe7dc352907fc980b868725387e98205e278f51363d6cb8e9a7414a59e722", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98088c7cf569552f0eb43ea2606d228704", "guid": "bfdfe7dc352907fc980b868725387e98f9ca2c1d0c8d2823bcb7a28d5f6d761f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cad53e1e8936bc0cdde467b2059ad0d2", "guid": "bfdfe7dc352907fc980b868725387e98544af219fb1b533826f0a5c5875f45fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ea835fa61921aa572bbb7e1b37210a9", "guid": "bfdfe7dc352907fc980b868725387e9872aa22cb2b9cae9eb52199b72229381a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab822cf32bfe455bcc26caf94e222eac", "guid": "bfdfe7dc352907fc980b868725387e98986b1d7278f61bfb4f1c0a4df6fe3991", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fafb563ea949d80d2cd845fb05f47ce", "guid": "bfdfe7dc352907fc980b868725387e981555d4b93a0ec0eda2a4fdee1047c284", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c859e2697cdf594160294635e8fcb952", "guid": "bfdfe7dc352907fc980b868725387e9823fab375ac5dc97b8a498ac60d538b5a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f5343447854a11e766af81568061e09", "guid": "bfdfe7dc352907fc980b868725387e98d493289c34bc0d014a56fc1165d26702", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98642dd12405a1f9ebb0b96004f1a3ff29", "guid": "bfdfe7dc352907fc980b868725387e983b2484800d3cca75789a72d6529e2b7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f89c3906cbc89f349b8c7c95b001d52f", "guid": "bfdfe7dc352907fc980b868725387e98cb4f877eafbf382407a9c61ba86a6d12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6b4c7d3a2c6a0e6f187dda140a2659d", "guid": "bfdfe7dc352907fc980b868725387e980d05bd0a9203faef72fc29fd23ef6e64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec163cba08b965f956cba9d9f8626b1c", "guid": "bfdfe7dc352907fc980b868725387e98feba97f27bd6a00852881c48b771f813", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f635f6fabdc047fea80a86d97838152b", "guid": "bfdfe7dc352907fc980b868725387e983d842fd120760044ff3670cd011a5fff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecb3e87bf3d67af2d4da2db6d5e212ff", "guid": "bfdfe7dc352907fc980b868725387e98fac016dc4679e03a17751e1f3bb181fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983577d812a2df9fe9089f4a550a458842", "guid": "bfdfe7dc352907fc980b868725387e985921df3e4e5e10ca181f7262f23750ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855d1fd54ebd054551a8b0248f5b4a0c5", "guid": "bfdfe7dc352907fc980b868725387e981ea1c127f1e209bbc2bd2fc50b982f4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891fa7406712c98a8b1bba58835a765f4", "guid": "bfdfe7dc352907fc980b868725387e9872e4ebbc51f4be0a0882ba1151b4b7ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869fcc0bce14cc06c2791c22c6d0bb5e2", "guid": "bfdfe7dc352907fc980b868725387e98fb80f3052436edc2e477bd6fd4d1b5ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849e0dff3ced723c5581590529d733dcd", "guid": "bfdfe7dc352907fc980b868725387e98eba57aa51c3bea3d79eaa528221657fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817f9b60ab9c03dc002ef508b55df54aa", "guid": "bfdfe7dc352907fc980b868725387e98cc211111e23855beffe3390887e0eaec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980332cbad54b622bc2989735b799790b4", "guid": "bfdfe7dc352907fc980b868725387e98f794b97f7750b1ad4fdf797d6d7c28bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f397ebdad314deb8b65ad7017e5e396b", "guid": "bfdfe7dc352907fc980b868725387e983a798ff9afc87f95e03b22dba4a2ee02", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983c788b23f98dd7d2ca40fdfd513ee11d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981208794fecdee80f989ba6ba5267fa15", "guid": "bfdfe7dc352907fc980b868725387e984796fb24c46293fdedcc9c4e1ca97345"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982254fb702c697f5031c791a81fb635da", "guid": "bfdfe7dc352907fc980b868725387e981193e29841b9c5fc4c3fb512b70aa745"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cf142266826534dcea944a7db6f0b9a", "guid": "bfdfe7dc352907fc980b868725387e98ef4bf0ec3d1819b3831d17b69f297663"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98297c023532db0e548250b1083845f8eb", "guid": "bfdfe7dc352907fc980b868725387e9824c115d80ff05a3dfd64de4b6b9d2763"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2170c0051dde00334e369924d773729", "guid": "bfdfe7dc352907fc980b868725387e989f8b0c18236dfbd71fd0ae2a7ed26f6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f97a3de553e77fbe0cac56b5ef295081", "guid": "bfdfe7dc352907fc980b868725387e98dd221f244929cc00f4ab039299379e37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870ea17be5f113266de21f7bfee4a36d5", "guid": "bfdfe7dc352907fc980b868725387e98a9643f1293c09c966e7047311d3ba980"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98378913fe57f1d9e1860733ed5c61220a", "guid": "bfdfe7dc352907fc980b868725387e98102839c53b4ad0e74f6a4d039b7abe64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebc169b57d563b617388bca55bb076ef", "guid": "bfdfe7dc352907fc980b868725387e984b325ac0d79da06cf3c240bacbacf27a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdd8db67e06971c28969cd2734681ade", "guid": "bfdfe7dc352907fc980b868725387e98adf035fc46ac6e9e5d6b56faed05cdd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0d567b1f235e8222cddc530264b99af", "guid": "bfdfe7dc352907fc980b868725387e98c3e5449043a9f54eb59cadba4bd80635"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5b8c5cc53e4c8761692fa5b75edee96", "guid": "bfdfe7dc352907fc980b868725387e9861eed1047b49b966789412e5249118d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c145730e4e3972316920b5ef41b14ef2", "guid": "bfdfe7dc352907fc980b868725387e988d7dcb990e6e23e9c0d0ad26086cf85b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98429fd42dab7a85f50d2d6eeb23178fdc", "guid": "bfdfe7dc352907fc980b868725387e98ff28e68b2a3146c4385cb7d5ff041b4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853b2d058817cf401b3bba216d9384e67", "guid": "bfdfe7dc352907fc980b868725387e9834e2fb2f5723b3f1ab0ba24a7728ed0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988adc32d5c14a73ae103cfc8a7fcc37d9", "guid": "bfdfe7dc352907fc980b868725387e985f2ef7071b7c09dce3b3232189d52c91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ce871e39e0c5b65a1035f471ec269dc", "guid": "bfdfe7dc352907fc980b868725387e98c2a5761639fd2489ce39f181f68b0770"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d7074be7718397d9a16a1ec3b40b799", "guid": "bfdfe7dc352907fc980b868725387e98caa004fd92b1d341d43a9ebfcb944289"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c928ab39ba2c9fbc571ef3029dfb40e", "guid": "bfdfe7dc352907fc980b868725387e986f60e95992ddbb276c7fb29e4b47fcbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807058b070dfe4dc70c7f42fabf5e768c", "guid": "bfdfe7dc352907fc980b868725387e9836e067b57d891eef12151086d539ba36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98209bdd68d7a5beb1d5e23e65857321ea", "guid": "bfdfe7dc352907fc980b868725387e9865e3f1be6a3edc2d7803219d97311270"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b057ebb70a060a44460bd1b395049f5", "guid": "bfdfe7dc352907fc980b868725387e9808a2a574c216751d559b0aaa88ff568b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf84d538fd88d957329428e74008e71e", "guid": "bfdfe7dc352907fc980b868725387e98fa7744c6851b703b481a2776ccea19e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806d9051d3d1ac7957be46d856ad5c490", "guid": "bfdfe7dc352907fc980b868725387e989b65f64879e1a083697d43eed83dc627"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c45acaffd2dc2138fd2e3a25d0a6b504", "guid": "bfdfe7dc352907fc980b868725387e987240326dfbe438dff41bba643ed38530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c2225abdb91bd5da8e939a0fe46e10e", "guid": "bfdfe7dc352907fc980b868725387e98a04237535099ce36821568b4131dfa18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2fabfa39f561de87a013884572f27a7", "guid": "bfdfe7dc352907fc980b868725387e98f457cc71c7111f8f2c3c7ffb04de8e49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898a5163fafa9a11d1fbf441e9b77c60a", "guid": "bfdfe7dc352907fc980b868725387e984ae202c20bfbce89e37b2300d82b0ee6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98578eda0f6e61919ac3882c503aa6da39", "guid": "bfdfe7dc352907fc980b868725387e98ee45e82f807cbe8f12f117171e8961d8"}], "guid": "bfdfe7dc352907fc980b868725387e988dad229a5ce29c5953c943a2f0766ade", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e9873d338e5c34bf130e7e618e790bcfec9"}], "guid": "bfdfe7dc352907fc980b868725387e98648203a552ea10ba13f01f0ce9a464e6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a6204b34f2a9e9c6645c81d293c3821d", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98c0acf765a2b74ec65594f5a48b2fbc4d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}