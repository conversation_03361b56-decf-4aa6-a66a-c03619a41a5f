{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980d91a182eef34a846d00887731f950dd", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98878ab7be79ea19865561ee8e58a55ed8", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98878ab7be79ea19865561ee8e58a55ed8", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9864184eabb559f58bc864b60ea9502b3c", "guid": "bfdfe7dc352907fc980b868725387e98dfa81521415c6004fad4ef4b138cb4cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816881e320f921415ea70687f2419b4b4", "guid": "bfdfe7dc352907fc980b868725387e98d9af888c965628280bc9abfc7b2b72e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec8bebdb1d0e808e967f47024817974b", "guid": "bfdfe7dc352907fc980b868725387e989c2a9320b87e76264f8155ab82421162", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a17b51b31b75140a86d5e09f0adfa191", "guid": "bfdfe7dc352907fc980b868725387e98948ced4ff12ad30482e11ac653e2143d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817955bedf8d9e7747b088ebe36beccf8", "guid": "bfdfe7dc352907fc980b868725387e988e0b2c0b2184c0a6b0a3ccf7bde1114e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c2c59a619a2e986f51868be977d5cfb", "guid": "bfdfe7dc352907fc980b868725387e987084874040146c6d4236a1878d9f2ae5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9c9caab7bc96d235509cf8386169d7c", "guid": "bfdfe7dc352907fc980b868725387e9807743bd25cec158bc9cc54c1ffa95db8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaee2482e8c4b40d0136efcde00c05ce", "guid": "bfdfe7dc352907fc980b868725387e986e804d1799daefbebb92f3e1a0993aef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f635638754f51c2e5eda6947ae44d75", "guid": "bfdfe7dc352907fc980b868725387e98888b258509e1783e70eb5159f17f4f9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b1e28f4a706f0010be342fc4fc1710b", "guid": "bfdfe7dc352907fc980b868725387e98e3a16d83bd758b0fabdff3526fe89f21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e08fdd9dac614019e15ec717bdd8181", "guid": "bfdfe7dc352907fc980b868725387e98a92366b5fead8a67e892261385b23f41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ea2bcb0746894251d71bcc1b80a00e7", "guid": "bfdfe7dc352907fc980b868725387e9858be426c1712dd571ceac2778ce0d99b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a5a1e973139d95bb1666c3981d24383", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98359ed5365a47055f6918d86284aab41f", "guid": "bfdfe7dc352907fc980b868725387e982d47406bf90ebd69f634e754d341283d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873b3c305e11f7701a264ac130f3bf957", "guid": "bfdfe7dc352907fc980b868725387e983a0b1f412f44093ce17674bd65897367", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e365b7bd3d7d5c6ecb70e878228d098", "guid": "bfdfe7dc352907fc980b868725387e986d1117328b79c9b09a40cbf9f80672cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98456964a73b2f20ea6143aa95185a7b0f", "guid": "bfdfe7dc352907fc980b868725387e9864c78bd8b936a452072676a9efedface", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b13d17eb73716a59a13e63f2cba567f", "guid": "bfdfe7dc352907fc980b868725387e982080f195ee2c80bc344af4e9595876e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aad88c8c229792472aff23e6a0b8a03", "guid": "bfdfe7dc352907fc980b868725387e98475e555158d28b008aa53d71df35e79e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b27028543aca83bf3615f89510622d60", "guid": "bfdfe7dc352907fc980b868725387e982d42638f1d81afec99e5be06c572a505", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880928cd79d58668b610ebe71a3f79d3e", "guid": "bfdfe7dc352907fc980b868725387e98b39051e98bc81051962c4f7984f3a9fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ed2a8cd205b4a7402d97e0637d9eeca", "guid": "bfdfe7dc352907fc980b868725387e98cc4e31dbb10c57415e9fa1fd3886782a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801da1817dbd64bff7bfdf0ffa56a7f3f", "guid": "bfdfe7dc352907fc980b868725387e98304a99847d09bba79e838cae526c6fd1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98198ade051ec88d4d5c78e2ebf8b19c4c", "guid": "bfdfe7dc352907fc980b868725387e982332162672dd6fce4a33dc6a86501735"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3a528b172a39ae7f34ae45cb2927dbb", "guid": "bfdfe7dc352907fc980b868725387e98f80093c99e86b7f77790e45c9a49426a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8b4b5d1024a8b30861fd0877174eccb", "guid": "bfdfe7dc352907fc980b868725387e9891fc745c4e4698bcdc61480856735e05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f236a0714830293fbee0d7e6e32d098", "guid": "bfdfe7dc352907fc980b868725387e98a57d51ec5ba1d6a903da91f5fa9f9036"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f4bc6056182fddda3309335a745752e", "guid": "bfdfe7dc352907fc980b868725387e98d4bc4c6ff669c35da13e920dbe9f0c1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987219275df09fec68cdb8764f92321612", "guid": "bfdfe7dc352907fc980b868725387e98e8aebeac1f143da26445a0c30e8b6b10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f549cf24030d2a13dd70825871acc664", "guid": "bfdfe7dc352907fc980b868725387e98b60407070bb375eda91f7bb44cff9be6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98948a6279c1c0326ab6c2451039aee0f4", "guid": "bfdfe7dc352907fc980b868725387e988e92bbfd33c9b6d13e8bd3bc16231359"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7590976bcaff0e1e9c043dc7a39cf98", "guid": "bfdfe7dc352907fc980b868725387e98eface4939c13cfff812e6149a7966afb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984095d36326cfa8100f397ed0e36ef043", "guid": "bfdfe7dc352907fc980b868725387e98e3ddcb7cdb679da6c10958d0844aef91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b44b6701fd1a2852a7adb6d7d4989ba", "guid": "bfdfe7dc352907fc980b868725387e98351be8e3513b325f93f459bdcef77161"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af7bcde5ded301535764e0e891d847af", "guid": "bfdfe7dc352907fc980b868725387e98250d940c86724008fc08444424a56cc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f372594e5737be21b5b6abf51d12077f", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98995a37831244b161442e2a2927f00166", "guid": "bfdfe7dc352907fc980b868725387e98d32228ff017adea22e20f1a460186e6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807e20aae96eb6574031fae25d1da14c1", "guid": "bfdfe7dc352907fc980b868725387e989ec6413494d3d5ad2568fbafafb36b0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1404e2b417e9f8494b79da8546bfea4", "guid": "bfdfe7dc352907fc980b868725387e98b9e7452acb92769f847525e0eea7b7fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842d9421f2805087896f8689f4f012e35", "guid": "bfdfe7dc352907fc980b868725387e9828b2ebaf9257a22117bd13c908fc420a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98818ebeec523bf96a08343ac2e7151d6f", "guid": "bfdfe7dc352907fc980b868725387e98a0d5aedc63429465c0ff1456c30d2831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af64a223545ec1498f92ed58dfc1a958", "guid": "bfdfe7dc352907fc980b868725387e98009fc52dab8ac8a63e9fe6f7b2f7bad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c7dfcf33629a1414015f0ed49cc89e5", "guid": "bfdfe7dc352907fc980b868725387e98c1f2ec9a43a09a879aeb3441912730b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f855ff062ee38e736085fc9c24280200", "guid": "bfdfe7dc352907fc980b868725387e983f6950b93dc850567a23c444bc59b174"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}