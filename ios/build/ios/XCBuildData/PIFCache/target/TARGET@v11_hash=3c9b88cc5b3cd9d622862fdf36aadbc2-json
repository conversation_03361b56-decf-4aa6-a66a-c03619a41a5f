{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a98611f38e5e0565105f585958269a4d", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982904b9d3d75ee7076c1b0dba47a153b5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986cf63764f823428526b8a7b2eab1d73c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810ab9610ffd50284882a526fa9c1be1e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986cf63764f823428526b8a7b2eab1d73c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989a9ccacd92b579d0759909df6b0a6fd8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9838df0c00dd4fa1ef17ea46945636db4e", "guid": "bfdfe7dc352907fc980b868725387e98753612407a43f84315ab909ae2704e9f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a29045f7b953e0ac867a6730ba2abf0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9896431377baa38bce4aa6bd9625f32001", "guid": "bfdfe7dc352907fc980b868725387e98b238f9e3d35539ea0a3af56c54aac26f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98823554e70047d105d72bdfa0c624a0c5", "guid": "bfdfe7dc352907fc980b868725387e982c98bf15a8245595b1fd441c96eacabc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98534a6ad7a6a3f15aad3f66d0690800e2", "guid": "bfdfe7dc352907fc980b868725387e986f9d3a8aa5980e706dada7a3a7093151"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846b4e8244fb22017d59ca1dc5a7a488e", "guid": "bfdfe7dc352907fc980b868725387e986219fd17a4deac04b6b16ec956605295"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ac7d3f8a7517ee70b639397325c8dfc", "guid": "bfdfe7dc352907fc980b868725387e98e01aeedf660605daf845d5c8d0cccb7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dbdcbbe1c49ca9526f7382c3a1d80c2", "guid": "bfdfe7dc352907fc980b868725387e98f756cc36c23a7fd38d778d25d3af716e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dd6468684c12a4482e8270ac2d74068", "guid": "bfdfe7dc352907fc980b868725387e989bee7014c9daf8aa659bf7c4af6760ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879768a81dc96b43dfe39c6e694c330af", "guid": "bfdfe7dc352907fc980b868725387e98ed73e4484afdf2af67b9cb75ed3f52b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dbeaf80480d83eaafa357022e836c7a", "guid": "bfdfe7dc352907fc980b868725387e98baff4a53ec210a7ac4959664b1e86652"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98471140b923d2a3f79e8d1813955237a6", "guid": "bfdfe7dc352907fc980b868725387e9857ed051f2f96d40d855508d9b8405dc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ec6ea4a83389ad008e3fe2b63fdcb9c", "guid": "bfdfe7dc352907fc980b868725387e98e30ec89e52d8946a85366139a6a4bb68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a72f2a62f399bb391cde3be6b3d12ed1", "guid": "bfdfe7dc352907fc980b868725387e9833739d50fbfa3ac57a3043e1c894a0ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837edbc2a9d9b41e43d43bbe997442763", "guid": "bfdfe7dc352907fc980b868725387e98dd02d1cfe14d9baac49fb06178d6c025"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823882401f5b1d65e3f455d9564490e60", "guid": "bfdfe7dc352907fc980b868725387e983b2d961484165df20f6e036e0bce1e3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3be723445091addbd42c48a6aa811e7", "guid": "bfdfe7dc352907fc980b868725387e987f95cf15c9f273844c7a73740348aadc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e31c1d4213b8c5f92b588269ab4f9103", "guid": "bfdfe7dc352907fc980b868725387e981293d9c7613cd46bd4f1c43f829750b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ecbb01f92a775d5ccae9e85d6bc44ad", "guid": "bfdfe7dc352907fc980b868725387e98e42c5a66170dd98bc2b59659ba06766a"}], "guid": "bfdfe7dc352907fc980b868725387e9851c1430e5e17c193718608d20590ceb2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9889c3eeb1814dcc6b68240524b9bd1c04", "guid": "bfdfe7dc352907fc980b868725387e9813e00427c22ff6afd86ab8c48b2420c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e98e96a5e7a06818e2919b219335c48c2a7"}], "guid": "bfdfe7dc352907fc980b868725387e98683eddff9ef816954c9c157a39ca6483", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984cbc71b544639dcf56634d11f774f3cf", "guid": "bfdfe7dc352907fc980b868725387e981dd7b62086f37eba24b9079627be3980"}], "guid": "bfdfe7dc352907fc980b868725387e98020e2f7596d5f2fc1a17e4ca092aa060", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e985c9de23706ec6ffbd0536cc5484b3896", "name": "PhoneNumberKit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f3bf2315f331e6c08a312dfd64b37cff", "name": "PhoneNumberKit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}