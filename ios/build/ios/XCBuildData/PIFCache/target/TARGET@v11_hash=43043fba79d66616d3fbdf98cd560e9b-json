{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b55da10ddd8296b69ff5371118e56a58", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802dfba1040b9826bcc620b6e29f5452e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9828884e4f3b55eb7afac6c8d24886b830", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98353c3596a6874e21a82e5cebb22618a5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9828884e4f3b55eb7afac6c8d24886b830", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a1caeb009dfd3c4f8a9eac8f99a0b6ec", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1ef728586cb2d056b5c8b4b25a4a580", "guid": "bfdfe7dc352907fc980b868725387e98e958f7c74d8cf82ca772151314733339", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f87c277a94ca90292fbfb4e75de7b0b", "guid": "bfdfe7dc352907fc980b868725387e9889018e9986950050023d79b3eb3b8d0d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc486e91a7b2aad904132f06b395a15", "guid": "bfdfe7dc352907fc980b868725387e98e0bfc04ba15b727c74a16e9edab4f4e2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98aa7647f5f17c2b31e264585bf9535b3b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e4be59fc18feb03e47026cac7f97c63f", "guid": "bfdfe7dc352907fc980b868725387e989d4889561e4934979285ff802f2c8ea5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bef133edef673fd96ae00c2f7957116e", "guid": "bfdfe7dc352907fc980b868725387e981470b7499f6611dc1063fccab8b3191e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf012b2a68b1e1c0b60be9ac255f1a22", "guid": "bfdfe7dc352907fc980b868725387e98375c6d58cef758c6a70b0e1702cd9bec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98559178c036c8a610a8f2ef089641f5e7", "guid": "bfdfe7dc352907fc980b868725387e988c7fbffa8cdbd0567068649103d5e08e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba08c1a68b4e99dfb09b33b22210719a", "guid": "bfdfe7dc352907fc980b868725387e98746c8d025193c9c0578c7abaec9b86f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b014a9d2a98dfecbbc720b4539fc9edb", "guid": "bfdfe7dc352907fc980b868725387e9825195d05430316dfd1edd5c8cf8d29c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cebc992a3811cc97a6d1cd258a35b88b", "guid": "bfdfe7dc352907fc980b868725387e9869b8819addd658d77cd118f981b613a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a5ab74f72b43123ef309a0445d8f0a5", "guid": "bfdfe7dc352907fc980b868725387e9887d27a6b2d76576bb4fa67a1022bef3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b2f6a786a991af4dd0f79eb47e01a2e", "guid": "bfdfe7dc352907fc980b868725387e98cce610a4a2cb277dd734640d0cb66bed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c74c3f4f4b0940dc97ca360062449ff", "guid": "bfdfe7dc352907fc980b868725387e98c30de3e517a6e210b2c9412f50aedc27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b92204c68acaaa53b22243c2eab4663e", "guid": "bfdfe7dc352907fc980b868725387e984b573e82f71c4d5cbe7d917933f14180"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827f76da0908eafc00d74c9bd972d7357", "guid": "bfdfe7dc352907fc980b868725387e98a67e8f87e941ec056a1e40f570652440"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b1fe5c85dab307c28b0436113d9b3f6", "guid": "bfdfe7dc352907fc980b868725387e981bb1d04cf742cc231041af4f6f92f5b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f68bf470923fa5ea72b3cc40e914b4", "guid": "bfdfe7dc352907fc980b868725387e981823629cf83430884568c3ea00d127d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98827581fd82d2c20ea87785cc6019312a", "guid": "bfdfe7dc352907fc980b868725387e98d0bee4252ce0b45e286430752a0c6f89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dc4a74b869a2ff409e85ddc7a6634a5", "guid": "bfdfe7dc352907fc980b868725387e989bc9da6ee5c8e57ad3c58ff72f6ccfd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986177d2db8a6279599596adffcb57e7af", "guid": "bfdfe7dc352907fc980b868725387e98ff859fd1a64d25b9eeda0657de3e66a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889e65aa8579fb54fd596ce1ebb67cfa1", "guid": "bfdfe7dc352907fc980b868725387e98dec2a69cac8cb77bb24af26ab32b5a9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6ddfd03797f60fb2384cc3b212faa6c", "guid": "bfdfe7dc352907fc980b868725387e98e4745b30d71ac77159f3c67d87729f73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b9693cb84da9d30a5ca400e6eb13542", "guid": "bfdfe7dc352907fc980b868725387e985f668d775036319e6390319ce165197b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836c6a43d9ddbea7cb2f45fe6de3768e7", "guid": "bfdfe7dc352907fc980b868725387e98376772ede1eef213a0dfaf1c3c4b1bc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b96234bfe5ab5924059ff6f4cae41d4", "guid": "bfdfe7dc352907fc980b868725387e985c92b7e9d513418650a719e5c4a6885a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98643b7c5d1ad6b778085eedaa1e1eca59", "guid": "bfdfe7dc352907fc980b868725387e98ddb677cd6172ddc22245ae5081454ba5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ad6de708bf3de153f1daad647767bb5", "guid": "bfdfe7dc352907fc980b868725387e9825e0f88a3dcee11fc22fe53d6d4c9f64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8f84ca069e91d43056ca45926243606", "guid": "bfdfe7dc352907fc980b868725387e98fe889ed303d8d9c98b105f855e88bad8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cf377282cd1c0a8bcba0409a8fc9893", "guid": "bfdfe7dc352907fc980b868725387e98425761264ca94cd96945659b57bcfd28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abe3a275f3632ce4e8fa3cf83649175f", "guid": "bfdfe7dc352907fc980b868725387e984c5c5413047ed7bd57346f4b41a17fc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832484af79251b9674101217f058f2ba0", "guid": "bfdfe7dc352907fc980b868725387e985112597849434dcfca33b169703276cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345552987bcfae235d54d4af83c75906", "guid": "bfdfe7dc352907fc980b868725387e989ad2437e993f7de01fdf52ec38c4112b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7ff9889143d9d59cd21018d92a1750c", "guid": "bfdfe7dc352907fc980b868725387e98a75bebdcaa5b1975624485c1038815f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98308c908574425ced2461ab11790588fa", "guid": "bfdfe7dc352907fc980b868725387e9899dc67ce0e42a94fbd5add743334554d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9f5b9dd35f8f8d5656adfc420b22eba", "guid": "bfdfe7dc352907fc980b868725387e98117c6209af8d7d0a0e52bdbd276536dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845874068c355a0e01e5f9b6c45fba370", "guid": "bfdfe7dc352907fc980b868725387e9866219fc358713f64ae9208a5e75b55f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5309ca0d5708642d008a1d6d4cfbbb8", "guid": "bfdfe7dc352907fc980b868725387e98877cb21ba623136fe61d7929e6e0afc0"}], "guid": "bfdfe7dc352907fc980b868725387e986ab5a09b9a4ad65c720d2be023caf286", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e98f63f763ddeca3aede9b5b5c6e2e3c953"}], "guid": "bfdfe7dc352907fc980b868725387e9868974109819156cae0001cab795a8d13", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c69589cf5eaaadee22bdd0a46f74f90c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802b8a2060b8f4c4f36a50487027e7bca", "name": "Stripe"}, {"guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay"}, {"guid": "bfdfe7dc352907fc980b868725387e98528ce098433a66c9a9c5c097217013f2", "name": "StripeFinancialConnections"}, {"guid": "bfdfe7dc352907fc980b868725387e989814132af5b72ab87e6f6046cac2a3cd", "name": "StripePaymentSheet"}, {"guid": "bfdfe7dc352907fc980b868725387e98caf0f30362a7eaf9b8b7f5ba71771d54", "name": "StripePayments"}, {"guid": "bfdfe7dc352907fc980b868725387e98bfacf038ceaf928d957d7e7abcab2e3b", "name": "StripePaymentsUI"}], "guid": "bfdfe7dc352907fc980b868725387e98f20376386b7fdaf72e36b18058deba48", "name": "stripe_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e987ca3631b039050a781963bb810e6746c", "name": "stripe_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}