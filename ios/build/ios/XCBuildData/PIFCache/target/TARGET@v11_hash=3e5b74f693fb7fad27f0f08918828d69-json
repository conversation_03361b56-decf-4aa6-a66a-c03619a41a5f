{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d3d00c4c1064efa32d0ffefe08248e35", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98998d84a8226ad44e0788275bc5d80458", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98998d84a8226ad44e0788275bc5d80458", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c14e37e06d3c86f49aca90f2300b36e2", "guid": "bfdfe7dc352907fc980b868725387e987a0a13cd5d5882a2e7d7c09c92b285b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bada258a3be71f6fc39ef244dc33f8fe", "guid": "bfdfe7dc352907fc980b868725387e983054b11c54be60f316c942626930e20b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b6d258affdf453fe21713d93d976974", "guid": "bfdfe7dc352907fc980b868725387e9874da70de807928ff3eba4068c8e392fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e2c67a1e84a2b21d5832ff31b8ddd6b", "guid": "bfdfe7dc352907fc980b868725387e987c3533e75eda5e9a65be1fb4f1a3249e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cb6f6434bd5268cc32b06752d3b4197", "guid": "bfdfe7dc352907fc980b868725387e98e0cfb487103d5901cecb6ed2a59118f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816560cdab7131fcabaa9ba6182e3779a", "guid": "bfdfe7dc352907fc980b868725387e98326ea9982e3d30315b293c0f4210190a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988607e546d6744cbc206792c9852c163a", "guid": "bfdfe7dc352907fc980b868725387e9828f4f69a16be70f1118f56c380828810", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98101e2396cf2c7f91e295b69836ae8b1b", "guid": "bfdfe7dc352907fc980b868725387e98df49f5b265b39ee8b07b7d409db53735", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0d2e085aff43762cb5fba7a4138654d", "guid": "bfdfe7dc352907fc980b868725387e982c49d0a7bf5f15edd2d65b093ca43922", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98501dd670b88ddc82056b19a752cbdd4e", "guid": "bfdfe7dc352907fc980b868725387e982b7fcc37aa3894cbc2c7d5a2325cd2f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6d2a88d54884e51159c4b6ea326584c", "guid": "bfdfe7dc352907fc980b868725387e98f93e95877d8bdcd55dd8f868c7a9fd45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ab8f01b93e158cfe88994fdf7c23efd", "guid": "bfdfe7dc352907fc980b868725387e98ee9149b49f49dcfdb24eebbf4cfbabba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed5f951e0032488f03d588312f4a4642", "guid": "bfdfe7dc352907fc980b868725387e98d2228a61a70c39998296e34de6b2298a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c535c450a053eb6adfec228102a94e9c", "guid": "bfdfe7dc352907fc980b868725387e989634ac118f0e133b63828e003a9358fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b276f8b1cb4467c034d048526ef80b22", "guid": "bfdfe7dc352907fc980b868725387e980209634815e24d24dfd4ad3d44d4ae0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b8fce766938128bb1a3b34e46f836e", "guid": "bfdfe7dc352907fc980b868725387e9876fd346988ee87b51172241f45569e98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e599224a12bc14482e5e0bb6ed2f6d6b", "guid": "bfdfe7dc352907fc980b868725387e985d0b07f3847612af82c47f3cec686625", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cc26e273b8b52fd30b38e8b4f53655d8", "guid": "bfdfe7dc352907fc980b868725387e9804073e770b6200464a38639fa9719a6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989aaf7e026c45fbfb87c904a8f384ad61", "guid": "bfdfe7dc352907fc980b868725387e98de9340648efb8d6755123556fd2fda7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887c79b18b52d535b45b5582bf113f4fe", "guid": "bfdfe7dc352907fc980b868725387e988087c60ca979f45e2c2dbfa20d596433"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98799c64224c3e652c488de6f2995df63e", "guid": "bfdfe7dc352907fc980b868725387e988f9f6930d9c375d6aa3696df93334cdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885f5c1b83e28045379516a9803dc1be6", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2e94fea31300562d6c4649722f1e599", "guid": "bfdfe7dc352907fc980b868725387e98ec1e63f1708e5c29d0146c0747a973c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98645cd925b0e4a90b0684419df0bfe55e", "guid": "bfdfe7dc352907fc980b868725387e98896819eb4102a484ded4897c7c0dcaa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b940335744537c252a79af2db132f72", "guid": "bfdfe7dc352907fc980b868725387e982aa180228b0cb94099bf7d20fc82a858"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bd152483fe46daa55a6ade74e1b52e9", "guid": "bfdfe7dc352907fc980b868725387e9801ac58db941b038a2c4ff7bc66d0cf18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836f782cade5f8fd28ac3c3eff37edcaa", "guid": "bfdfe7dc352907fc980b868725387e98d8074c7c1e0612cc805f25ab17a585e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d16602b6adb66dc8db2dd9ecb318584", "guid": "bfdfe7dc352907fc980b868725387e98fe43dbdd779d0e231f3703ee9113acbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bb1953c04e4359f96df46fb300f6745", "guid": "bfdfe7dc352907fc980b868725387e98b2fdfa3fddc4b5f9e71d08e570ed1016"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f19ad4843f9bf15f6886bed25c4eb2f", "guid": "bfdfe7dc352907fc980b868725387e98f5443c0f0881944bd625ea1dd144b219"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6dfb11ce59d6fd2ba165bf59e0907b0", "guid": "bfdfe7dc352907fc980b868725387e98a42383cde6e6f6f8fc29a922e29e871f"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}