{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a28c871b9f61db7cfeb153e7d6e2f15a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_osm_plugin", "PRODUCT_NAME": "flutter_osm_plugin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980a114b328fadf3e71f9d7ad93f1e0e58", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98364f3bf78f6d7b4c402ad59c3340efcb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_osm_plugin", "PRODUCT_NAME": "flutter_osm_plugin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98283189b3afbc4fcd51922087d47274c1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98364f3bf78f6d7b4c402ad59c3340efcb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_osm_plugin", "PRODUCT_NAME": "flutter_osm_plugin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9889c498a49ebe9c38ca56fcd35dd93eec", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e6237bbe6bfad0125d204e2029727906", "guid": "bfdfe7dc352907fc980b868725387e989c6edb273e117d13114e9f1213e2ac16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af4449c818d72b0d2601cb19840f3f9f", "guid": "bfdfe7dc352907fc980b868725387e98b28b453ae142f19ccad00bb20db0e3bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989922041e439cc665470238e941a65598", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98577f6c75fd041ceb214b2271fd974b1b", "guid": "bfdfe7dc352907fc980b868725387e98b1a72adbddde96e5c1d2108c28dbb7ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981af45a05d964639b7fb251cf380d16ea", "guid": "bfdfe7dc352907fc980b868725387e982aa4b6c8fdfd2dafac87ff9dcc835cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98102019a2635c362d3973cdaebccdbe34", "guid": "bfdfe7dc352907fc980b868725387e98aa822c9dc0c9ecbdece6a2422b2659d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829a93b6d8845955ca0465f72151b64cd", "guid": "bfdfe7dc352907fc980b868725387e98035e46403632aba62dada39f3bcd3b18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fab769eed2be43c1f889c4413e16fb78", "guid": "bfdfe7dc352907fc980b868725387e984bc577fa047223dc45aac9d013c1b87c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ac76b76ab280f2906566aacf8024e50", "guid": "bfdfe7dc352907fc980b868725387e98e95caf4b94c4191942089b3428c19d10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98442cef242af94dd08babfcb356c589a9", "guid": "bfdfe7dc352907fc980b868725387e98b8e1c6349edb489eca1e69fea98cc227"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887514e9edba7a501bc3b07f3650915e6", "guid": "bfdfe7dc352907fc980b868725387e98441a1570457861f279be00040f314971"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980acd1be6c8293560e599129fd1efc3e1", "guid": "bfdfe7dc352907fc980b868725387e98a5be52f9b786916568d35dba6d899fe1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d4be06c4993b5f0f505358eb413b174", "guid": "bfdfe7dc352907fc980b868725387e9808b3dfb3de449ee088a40d4583e4e671"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a65d5c0be15680fea39ce0358f10f6a7", "guid": "bfdfe7dc352907fc980b868725387e98cba7affa4b045247b524ce2ce63dfa9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988476be13c421fc2b1b09a0767f552ea4", "guid": "bfdfe7dc352907fc980b868725387e98b21b6e9d3719c5822747ef207b2abad3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e39eac5e4e69778afb291c240ffa73a0", "guid": "bfdfe7dc352907fc980b868725387e98ac292ab957369d5525cc4898735f0e28"}], "guid": "bfdfe7dc352907fc980b868725387e9830090b3fb9727f00cc0013a0f7344d7e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e98a704d0467b05bfa732899ce5309b8829"}], "guid": "bfdfe7dc352907fc980b868725387e982b9246c0db286cb8dd905319f49d8d29", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dd629b7b65ed0c0af290346ae6f9260c", "guid": "bfdfe7dc352907fc980b868725387e98d9da0c06c92fd062dd0dded30c60ca37"}], "guid": "bfdfe7dc352907fc980b868725387e9830000545cd5495d54523bb6dec92a508", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ba3679b4c4428e6a6d83c5308d3af99b", "name": "Alamofire"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98deea8f469e33b13ceb7aa3e97810c0d3", "name": "OSMFlutterFramework"}, {"guid": "bfdfe7dc352907fc980b868725387e9873d2f7f3c0ba6d1fb2b8b6f08bbbf48f", "name": "Polyline"}, {"guid": "bfdfe7dc352907fc980b868725387e985906e783cd19a852cc9239174f1ec1db", "name": "Yams"}], "guid": "bfdfe7dc352907fc980b868725387e98103762ef0740dc5525f04361cd4e3800", "name": "flutter_osm_plugin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ee27ca28813d9c560284de302d27e0cd", "name": "flutter_osm_plugin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}