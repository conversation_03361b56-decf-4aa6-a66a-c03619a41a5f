{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98114879d888607b1e14f730d1e2cc76df", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9859ed185bddda9fac98fb955dbede7af8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d93ecc798c9d234183b16d02cec4fff1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad3933a0aa7d10d295b0b450348462c6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d93ecc798c9d234183b16d02cec4fff1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9800a96723981eef88cf31d8c7236d90f0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98116c182863dd1ebcc5294b3e4ad56de3", "guid": "bfdfe7dc352907fc980b868725387e98c4f5c98f0da774a21e280eab7fe057a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6078c87460be669580a744ec62b2e91", "guid": "bfdfe7dc352907fc980b868725387e9839fc6c7c664204e244b373b460a9432f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866e767e8b7e9ec5d351c5d08eb937966", "guid": "bfdfe7dc352907fc980b868725387e98427a666d7183895e3731d9813f412e20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c15ba65b99b1391056b5ba4a6404d59", "guid": "bfdfe7dc352907fc980b868725387e98e6727dcc8f1485e917b74fd4bef0a6ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b7e72e785a30d60f65127fc8ff07e51", "guid": "bfdfe7dc352907fc980b868725387e98df277450d913184a2fa4d6f95009ce33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895910864c796a0229d3a09e21b1c1f20", "guid": "bfdfe7dc352907fc980b868725387e98eb2550ce05cbbca7cd449a6c4ff75f87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6db1c673171cbcd2dc72405b7a0e94c", "guid": "bfdfe7dc352907fc980b868725387e98638198299c71ff4f47d5a92a029a6b66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cfe04b0bf3bf4961618430f803e055c", "guid": "bfdfe7dc352907fc980b868725387e983f2105b19a45887ec0cd99b8f4098a91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883c26cbcf9f2577031f63f6a84e447b0", "guid": "bfdfe7dc352907fc980b868725387e98705be49b2174f3047c35324fab7fba8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af6c554ed710605dab48a812f3dde9b2", "guid": "bfdfe7dc352907fc980b868725387e98552ebb95fb1f5d91334db314a2781a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b7f308884fe55496d4923129171df7e", "guid": "bfdfe7dc352907fc980b868725387e9890339403453f254130360d2034e1ca48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e6bde479c6312be2fb533da8401e8a5", "guid": "bfdfe7dc352907fc980b868725387e983a5f24f9d5e80f37cf291255e141080c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a13d412caee3fd73e0718ed3f812891a", "guid": "bfdfe7dc352907fc980b868725387e98100c4dd103ffa81658874e2b2d85d5e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810d8bfb9408ac8887a20300516004da4", "guid": "bfdfe7dc352907fc980b868725387e98617f042988b16886e7cc20ec65a58165"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1447f94d9294a5aed66a0cf965a8136", "guid": "bfdfe7dc352907fc980b868725387e9865b0896451f9d6cf2283a039fbf63cec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851956952805a3c3c059572a7f78b083b", "guid": "bfdfe7dc352907fc980b868725387e9817b2cd278806279c92d05559de20d64a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892339caf098b351ccc634e5589bb738f", "guid": "bfdfe7dc352907fc980b868725387e989387f43106c5ec26f7c28c4ccdaf7a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98618b148f6cbd00a81ac29127da1a34c4", "guid": "bfdfe7dc352907fc980b868725387e98a579ec35ea48344f451f640f5133a97c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab0621bce4791800af0cc11aaf0ef9f3", "guid": "bfdfe7dc352907fc980b868725387e98247ce5c43b725c1b4d8c5b17a0915e14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0695f89490c9eb1fc57fcd6eec1519a", "guid": "bfdfe7dc352907fc980b868725387e988f1107f583b9dbd85850564d62dbe5ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987451bc31b808de1f71a07e0741dbc526", "guid": "bfdfe7dc352907fc980b868725387e987199af7a0850f7694381ed347c4a2089", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98769ab9ba047757679922c6ee8e0dc2f7", "guid": "bfdfe7dc352907fc980b868725387e984c3ddb72446e455ac3be9c0c8232f685"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855bfcd761297afd894316c462b76e9ca", "guid": "bfdfe7dc352907fc980b868725387e988e030a7305ce8e1c8acf4e05f72438fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835af342f4305250bd3bcd1a3c157380d", "guid": "bfdfe7dc352907fc980b868725387e98a6603a3db649375f87186c6acf3277e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981303a8232bc55b482e30b5ff9add3758", "guid": "bfdfe7dc352907fc980b868725387e984378d226f2fd781453f3a6279bdd9a5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98549a74a531214ed3b784f6ef737773c1", "guid": "bfdfe7dc352907fc980b868725387e983aa61192ae40fcb1e7747889e964445d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a0cf1b2978f806ae1280de1883a38d8", "guid": "bfdfe7dc352907fc980b868725387e98091769d6d3ea2dad8c8511b5d34270ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826c23571e18da5851d0f7b8ebee9b2b8", "guid": "bfdfe7dc352907fc980b868725387e989879c076e1ae8cea441a6b6f2a225e71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc79b974cadf62764f4e27d1a12991cd", "guid": "bfdfe7dc352907fc980b868725387e98950714569cbb291ac99cde4b378f4651", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe10fb2e27fc289ff16d8284758906b8", "guid": "bfdfe7dc352907fc980b868725387e984a1d9ef740af0331a31f3d263123ae9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d840595c34088fb9b7495d4f0462d18", "guid": "bfdfe7dc352907fc980b868725387e985e82d340fc8c2378a4a2148d02ddaf80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898598a32e3128db36f348e882fbb891f", "guid": "bfdfe7dc352907fc980b868725387e980a2586542aee47f50069b4e609e95eb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fbeb7ac71f3bfb09eaafbdd2948ce0b", "guid": "bfdfe7dc352907fc980b868725387e98ff329000b4f20b17a27ea21ce0626977"}], "guid": "bfdfe7dc352907fc980b868725387e981aded8a02e4b1e337a315b029e4bfb91", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981e18abba9f61d72e77bb00581f2227c5", "guid": "bfdfe7dc352907fc980b868725387e983e6dcd61148781ca855fe92f7db09b2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee5bc680310553b219e8ab1837915e42", "guid": "bfdfe7dc352907fc980b868725387e9866e5064fd7a9125a6b713372d680c524"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98754c4c44b67b4d58639833daa0c3e2e8", "guid": "bfdfe7dc352907fc980b868725387e9872d600e0be7a46c569226c942040fa57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a550817f43b28845d74830f79379d62", "guid": "bfdfe7dc352907fc980b868725387e98ee427f7940a070d745623c66d8c1b97b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849566abe4b2a69b2d8021948685a666d", "guid": "bfdfe7dc352907fc980b868725387e98808c3f107c3150d987a622f7d1bbda56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853ad6e2749331d61d1404df9ef041fe8", "guid": "bfdfe7dc352907fc980b868725387e9861eaa32b5adcdd7d6ee6553abcbf3d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873d5dd7f4f579a266805d21d290df221", "guid": "bfdfe7dc352907fc980b868725387e9811c8c0afe8469cb9c707bf9746a7ac2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808cd7d5433056112dc5c245282ef9d15", "guid": "bfdfe7dc352907fc980b868725387e9897e87d5e4218ee912acae6064cce6234"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828debe482a15b86c10353ded198a773c", "guid": "bfdfe7dc352907fc980b868725387e98d83895c00a5d47fee98859d5c618a8d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa6c04c16234d844763a5623e2b353b6", "guid": "bfdfe7dc352907fc980b868725387e987e404fabd4e635a6a708573622e6eed2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad67a07a5f427a8a63c7fb8908095700", "guid": "bfdfe7dc352907fc980b868725387e98c4f914657109c26cd7a882f091e4852b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98723d857e6dcbd38fa1a5b00366c04569", "guid": "bfdfe7dc352907fc980b868725387e9855dbb60637d32e33e42287f4d9fc792c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac2daf221499aa5220fb1f072a1cecc2", "guid": "bfdfe7dc352907fc980b868725387e98158466ccbc0debc03eb00b4ca0c3afc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e04ba90c73a227074396eb0105162837", "guid": "bfdfe7dc352907fc980b868725387e98801320b4a527d37dd02e224865a958b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b44c3af20d8ff8720cbf47620bf837ee", "guid": "bfdfe7dc352907fc980b868725387e98d7099315dcf4f878890c3932418d5154"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e5d391020b2f692a970460517765908", "guid": "bfdfe7dc352907fc980b868725387e985966cde74b01bd2ba48d65abbebc9c21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce250ca0f520a8e068048171a25e0ae0", "guid": "bfdfe7dc352907fc980b868725387e9828395b9104b94e746bda0055712ef66f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9f60a3341c42244309d7d99d16cc3d3", "guid": "bfdfe7dc352907fc980b868725387e981f81db25bccfef163e8f58db59961417"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c31c19bf787a8a300902c433fe397c7b", "guid": "bfdfe7dc352907fc980b868725387e9839786429cdc16b47ba317f6fc105d1bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98400b0984c266ea8e9208f32d79b4542e", "guid": "bfdfe7dc352907fc980b868725387e9874acb33857fc8088fa06c76d5b5baaf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c79b4c6a1de04fd75331ddbf45c4af59", "guid": "bfdfe7dc352907fc980b868725387e98b97d363b0575d23b26a4f5b8b703ec17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826ca558c5af81a9a2cacdff255f20ac6", "guid": "bfdfe7dc352907fc980b868725387e985519cd16c390bfceaab7853a823fb459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eb461a794a802829936aa87aa2ac01d", "guid": "bfdfe7dc352907fc980b868725387e98b36f75eed1c780aaf0f08cec0eb278b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1e2494e23a59202dc12908515ceaa6a", "guid": "bfdfe7dc352907fc980b868725387e98e41590211de7b295360237849f5f3e4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c38bd83e742a090c7a845e45f6dfda4b", "guid": "bfdfe7dc352907fc980b868725387e98943ae06af83ca3f01d2ab544826ef749"}], "guid": "bfdfe7dc352907fc980b868725387e98a321da4fc5869c3c238cb23e0891a680", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982718126677df410dba078f9df2ba6e57", "guid": "bfdfe7dc352907fc980b868725387e983171ca607709ba70b7546ebed4586225"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980146b46958d3013b9ebb563a3fa748d0", "guid": "bfdfe7dc352907fc980b868725387e98c2d16e607333c752087b1e4969575d2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e985bf343262a16656a91a507b09bce39f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d525ee330f935ddde249bdd0c122a3ba", "guid": "bfdfe7dc352907fc980b868725387e98130ec0c2334a400e82d3ad32be151f71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d05a97faeb775352b784fcd3a04da5fd", "guid": "bfdfe7dc352907fc980b868725387e98d54612bc5779951b16ff59fe9db93e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880a17b68e7b3f6ff442aa03cc954387a", "guid": "bfdfe7dc352907fc980b868725387e9825424d0feb24a4df1e3c56fdb9919796"}], "guid": "bfdfe7dc352907fc980b868725387e9897accaa9157e6d4daa68cd6abfaa8465", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9892138fcdebd2c18a4559605a27420448", "targetReference": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5"}], "guid": "bfdfe7dc352907fc980b868725387e98e408fa91ecb9a202b7a733d7c8b68761", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5", "name": "GoogleSignIn-GoogleSignIn"}], "guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815509a5aa54606eda7171e744ada7414", "name": "GoogleSignIn.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}