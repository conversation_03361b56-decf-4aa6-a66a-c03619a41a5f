{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d3f3bf1bfaed354fc6c585b4cf62c94c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Yams/Yams-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Yams/Yams-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Yams/Yams.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Yams", "PRODUCT_NAME": "Yams", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.8", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c039ee23c948a07d0d0efc2b81137dc0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9865a7bacb7fb6c62e2b296b54785b0413", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Yams/Yams-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Yams/Yams-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Yams/Yams.modulemap", "PRODUCT_MODULE_NAME": "Yams", "PRODUCT_NAME": "Yams", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.8", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9861cd2c30d21c9230a2b54e7f90ca831c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9865a7bacb7fb6c62e2b296b54785b0413", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Yams/Yams-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Yams/Yams-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Yams/Yams.modulemap", "PRODUCT_MODULE_NAME": "Yams", "PRODUCT_NAME": "Yams", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.8", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98617058fbc086ed919637a26cc29415ad", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c4d0ddf0066d22f2a42a3c23306affe", "guid": "bfdfe7dc352907fc980b868725387e9814a513346db670eba984c4eb61c7b86c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d8c4fb776cef6fc715247d1ff7f9ffa", "guid": "bfdfe7dc352907fc980b868725387e984da78f1d1d3a5f3ec5acd853cfe189ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c87e41ac5988999e98d241d3800b51ef", "guid": "bfdfe7dc352907fc980b868725387e98f0e4586d9d6a472231c5e61ede6db816", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd650ef7b476185c12b7bafe202c3fef", "guid": "bfdfe7dc352907fc980b868725387e98caedd4e113ac120f2e0d20faa8bded4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98696c1dfc4589a13fe4bd683cf6acbd0b", "guid": "bfdfe7dc352907fc980b868725387e989ee7fc0f4aabe3cee83107a1869875ae", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b7e263a3c71d14bbbf62de43920da620", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e6ffb8956b05ff4d71ba2b1ef05b1c79", "guid": "bfdfe7dc352907fc980b868725387e98ed0a47420565b982d207196e62c58086"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d9c6d21aa60658a30a8389c7fd9173b", "guid": "bfdfe7dc352907fc980b868725387e982f9c027db5611bb23d6ee24046b0b41d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c888046509342341d3032f94200dc138", "guid": "bfdfe7dc352907fc980b868725387e9837b55869805bfc36119bd01429e2e76e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98400b0197fd0b53d7ddb0a13e4641d178", "guid": "bfdfe7dc352907fc980b868725387e987d470940523e854564a680cf4ae0130d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841dc94dc565d898ae455aab83f44cae2", "guid": "bfdfe7dc352907fc980b868725387e981b04c7153f2a5185bf49b6c463a87f9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c75f0dfa2bce4daa952a163513a56c69", "guid": "bfdfe7dc352907fc980b868725387e980f93430acdafd49366b6c498e0b76739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e47e6ba3a06711292be0cd09a103f2c9", "guid": "bfdfe7dc352907fc980b868725387e983a33022940adc7a72da20de351a2e44f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf8d41f873452c938f8c58e32fedeceb", "guid": "bfdfe7dc352907fc980b868725387e9816f97aa31f97e64de339d0f90ec1385d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a811338690c5d4a9a194c61fe6e42a53", "guid": "bfdfe7dc352907fc980b868725387e9811ceb96d6b1d8eb472307befbce190d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ece56788e27a54e350a267764600ea7e", "guid": "bfdfe7dc352907fc980b868725387e98629cbb4f91fc3ab6ea050ab916728a9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987be8aaa0394b722c5c4b96f036f402aa", "guid": "bfdfe7dc352907fc980b868725387e9850bca9dd1bb7d6e63a23fddb8299f10a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866a6d03bd537076a9b4f3a2dcd92fd89", "guid": "bfdfe7dc352907fc980b868725387e98f05b0e904853173dc3167f0464194077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98734788c903477323307e79f918a4e4e5", "guid": "bfdfe7dc352907fc980b868725387e98796d8d12d1d905c8c2a090a54ef499dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a171dbc7112a285bc500e95cb95b5e0b", "guid": "bfdfe7dc352907fc980b868725387e98d37b59e0b17a09191b451aa5ffb27b4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a97b6cf54e6eb2b5281b4087a6264e19", "guid": "bfdfe7dc352907fc980b868725387e9867db45ceb3d52dd0f66984d80efd63ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818b3ce3f8ead49edf35269eb75981f27", "guid": "bfdfe7dc352907fc980b868725387e983910590632169299e62d9c387f3a8a43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856cfb4c72e0f9b7d3aa09a514e3f159f", "guid": "bfdfe7dc352907fc980b868725387e984ae1ece8fe36a1f92864e926133c9337"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c76343fd25fe7f668568014c88fe8662", "guid": "bfdfe7dc352907fc980b868725387e984598244644114b6fcda8bf7a09e8019a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98710850843b702eeee20baf62b6c9b1da", "guid": "bfdfe7dc352907fc980b868725387e98ebe095607d259dbc00d4a3c10a6711cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980924c03ea391d85e13f2da73a44d886d", "guid": "bfdfe7dc352907fc980b868725387e98b4daf5c2443019515618b7ff6b3ef879"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987edad71ec8c08d5f18a591123de42f94", "guid": "bfdfe7dc352907fc980b868725387e98e16d93afe54a2eb8dbd965da3a1a3ecc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d21e672a1ce9afb9447057150edd81d", "guid": "bfdfe7dc352907fc980b868725387e986a635c55a0491986b2b52b2153126f36"}], "guid": "bfdfe7dc352907fc980b868725387e98d322e35b9d74ddf81f16567e429fa50a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e9812dd137d730d442c95ae02e9c9a13c12"}], "guid": "bfdfe7dc352907fc980b868725387e9872fae1d4c55771c673488227363ecdb9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c565e68f9c7ecca1751b024a694df1b6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e985906e783cd19a852cc9239174f1ec1db", "name": "Yams", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983573af10de618087816a99161b763dc1", "name": "Yams.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}