{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e31e9c25078b75f55b5904c87f203fc1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9840a4ba7bdc22b7ef3501aa393bb4c9f3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9840a4ba7bdc22b7ef3501aa393bb4c9f3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9893ae9e3b8838cdb5d6befe24509724e4", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893ee42f5f97cb0e93f3a220361654b26", "guid": "bfdfe7dc352907fc980b868725387e982c8787d6921c468346624c23bb168d70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b77579ee8cb0ac15c1f23592401b8ec", "guid": "bfdfe7dc352907fc980b868725387e9805f3a9103938cfe6e0df58e6a05e87ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd9115ff673d6900960fa25143bb17b2", "guid": "bfdfe7dc352907fc980b868725387e98ecbb906e13dea7d314cdb97d736bfa1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987061e7ff063f9f1ca76429d07d29f78c", "guid": "bfdfe7dc352907fc980b868725387e985e9f66c2aa9573a0074e3e1b5913be8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afb9f3847e13f375f5591e2277bfeef3", "guid": "bfdfe7dc352907fc980b868725387e981fdc8d36fb905b380d63914dbdf7c0f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834b28398f1a6bb29fd78206a16e3a2c6", "guid": "bfdfe7dc352907fc980b868725387e988bf7db7ac201a9de77dfad76022ce5a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804708b859aee4368360b038f2e09461c", "guid": "bfdfe7dc352907fc980b868725387e987863867c927d565235fec1279738e4c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eee7c8fccd415ded586947426f81f746", "guid": "bfdfe7dc352907fc980b868725387e986869409a283d50b314c08672babee7f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be274316f251d5d4500395917e7beea2", "guid": "bfdfe7dc352907fc980b868725387e98502166cdc54abd9fa0dbf4dee165d6e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a347d549cb79508ee6707520c41b54", "guid": "bfdfe7dc352907fc980b868725387e98240d28a253d43701fdd5a05293a314af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e24ec606cd4554e7c28fb2c38c469c35", "guid": "bfdfe7dc352907fc980b868725387e981f129afa90f61045a3d53eb46cafc864", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cbefb4033e51ad6f7b45f2708e1c790", "guid": "bfdfe7dc352907fc980b868725387e983e5e44dbd2b5510fd741d6b5355ca164", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98125408144b3142460baac20954bdf727", "guid": "bfdfe7dc352907fc980b868725387e98d547a7f90030bffc46f93531d9d0f026", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984a5c824e58cb9e59de9595e4a3c1c9d4", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b380e7adb3a6512b8c30e631bbaf21d7", "guid": "bfdfe7dc352907fc980b868725387e984aaba992f30ec0986b87ea45ab7bc266"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896978d6859c9e070b5d92ab78d194d98", "guid": "bfdfe7dc352907fc980b868725387e98456982fa8db7febc42f127cde45dc5b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98790c33b73bd818b08ee1d05bea14ca88", "guid": "bfdfe7dc352907fc980b868725387e988557baa7c8dde0daa23b95e5d743616d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821da4cc8204d0449592662e53106b7aa", "guid": "bfdfe7dc352907fc980b868725387e981e2e616a3c7126b32bf677287501bb3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd30ebaad9dd2f1ace27f22f1c54b806", "guid": "bfdfe7dc352907fc980b868725387e9874fdc432bb8ed43ffc8c3841c063cd88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f67a033f6e9b689aff22141f7668612", "guid": "bfdfe7dc352907fc980b868725387e985e0f7ed9488219500fc3e3f5f66d3237"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af7debced0afd47813d1b731924e69e4", "guid": "bfdfe7dc352907fc980b868725387e98f4e315c1af0d40ee5b232ce14d1bdc86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851cd5f2e54be492d37087f38bcf027dc", "guid": "bfdfe7dc352907fc980b868725387e980d4fc13305b72c1852b7e910f79dd212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852704cf3303bb93e4d90f38fc06f9e71", "guid": "bfdfe7dc352907fc980b868725387e98032ae7b8e348ef499cc7c45387783cdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c965ec550bdefa7c76280bfdad6d48bc", "guid": "bfdfe7dc352907fc980b868725387e98f8f406de1eca77ba90c62439e11a0390"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb1f7d5a60efa9f64a7f1a7a00c83129", "guid": "bfdfe7dc352907fc980b868725387e98df297c728d3cb687e175390e938b779d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890f2abff9a26b8d25ed602e447e5bf72", "guid": "bfdfe7dc352907fc980b868725387e98e39809e9a4c115e985b07f3c2f81aa31"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}