{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9836101c6a694d7ebf43cfac792a0b9db5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98326fbc0a17b76f819abe3a580f85c98b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986566cb7baa3fc3ba6fe850f15f44959f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983473ac3b032ee737d4a7d989e5006de1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986566cb7baa3fc3ba6fe850f15f44959f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98713c0d3d3181f07717efcd574607c08a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ed8883c5257682b1d78b2ee610e69d0e", "guid": "bfdfe7dc352907fc980b868725387e98bf246acbf5c6cfc451673d6c481d3c56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbbcfadbcea3abb94ae9819a14ce9aab", "guid": "bfdfe7dc352907fc980b868725387e98526434ef3d1a8ae8b965b7259a20945d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7848b4d7f15468b94125f7aea90d00b", "guid": "bfdfe7dc352907fc980b868725387e9859243cbbed6c8182a40dff0ec6639e6c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867169ac7a41db0485fab98a87ebe562d", "guid": "bfdfe7dc352907fc980b868725387e98c8456f67c52b06b143b00b3e56212f96", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98085c40a477b9dcecbf1d0b42fc31dda9", "guid": "bfdfe7dc352907fc980b868725387e98564331b4539eb862ad4726f73aecfb53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf2a3403094c0c1d48b107c404114d00", "guid": "bfdfe7dc352907fc980b868725387e981cf0d3ac15ec66cdba87a8d3ceaca83e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987805b0af2d9a70008dbd9453ab156f4a", "guid": "bfdfe7dc352907fc980b868725387e98e042f33ec9a5e4f683ce634099f44501", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984729cc2b8ced422f31a3f6eedcfae1df", "guid": "bfdfe7dc352907fc980b868725387e98ba47e5745ff30d7f200173002fc634e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98073cc230e09115ff5ce672ce21437c6d", "guid": "bfdfe7dc352907fc980b868725387e9845cdf0f4413038195bd4ecbbda643ab2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9d7fcdc7671f3000de2a0cb4eba5a91", "guid": "bfdfe7dc352907fc980b868725387e981a08e37c7a1e42bfd417f60936cc77cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989af3ff64054c5c4917cd535abe0ed3b2", "guid": "bfdfe7dc352907fc980b868725387e98b10d9e768a983523e6e4fef0a90c570f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870c7263f3071234c7fd2935c873ac570", "guid": "bfdfe7dc352907fc980b868725387e988ae344f7500de69749ce52fd7c8d9738", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985674bd92031d4f18ffa2f54a15ac7b0f", "guid": "bfdfe7dc352907fc980b868725387e984bab9828919db72df6b32097cc598c81", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813ce3996a1590839e57f9b6834863fb0", "guid": "bfdfe7dc352907fc980b868725387e98c6cc3e4857217e1292cc606430535cf9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bd1e06f5b867cb7e2ec5e9fd5704b55", "guid": "bfdfe7dc352907fc980b868725387e987e1c64f6308c1520673c6a41314de4a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3fd4db53f9ef07211d1b19687ab4314", "guid": "bfdfe7dc352907fc980b868725387e9866667b3f95dc2ff5170dcec26467ba1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98504028542d0247bf37b971ca0825a020", "guid": "bfdfe7dc352907fc980b868725387e9864fd01c29a1d9cefdc8e88f8d5d295eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845bf621c420e1dcdb6c5b5d989397db6", "guid": "bfdfe7dc352907fc980b868725387e981788d84894c41a5b78b25b75af9a4973", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841377fb4967d03ed61f4bb9544c7df02", "guid": "bfdfe7dc352907fc980b868725387e981eb5a8a5dc24b976058504aef139057f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2d34fe90c7c91cbcce8c94613eb3676", "guid": "bfdfe7dc352907fc980b868725387e98519945036138c7b4f5cc61e839f135d3", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7a7b1875dd1cfae34b6afee44bc6974", "guid": "bfdfe7dc352907fc980b868725387e9873e3bc53835bf1b8e03ded8fb65de1ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829d11725777776498391421d627b3dae", "guid": "bfdfe7dc352907fc980b868725387e981e061593cb81de222cb59e3f017b4dc0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ddee2197544dae0071f7bf734b16fb54", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982506d0bfaa43f6b1fa0a922c396cabe4", "guid": "bfdfe7dc352907fc980b868725387e98edd9a1837a4c57ec6964dfdc73e0ff9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddcbf7b854a45f4de3eeffad0219c52b", "guid": "bfdfe7dc352907fc980b868725387e981e14397b13a712b536f262c2a264c7bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98132877affc4cd1abfa47d9e5e6c0999a", "guid": "bfdfe7dc352907fc980b868725387e98979ab30c4ab0417ed1863c0e323c51c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b072df33fb9716e694054c09d585e386", "guid": "bfdfe7dc352907fc980b868725387e982c456c30eba5820dd2905d9151a858c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988daadade3d58e17ec5fb9fca9b8680a9", "guid": "bfdfe7dc352907fc980b868725387e98ab1aaeb7ee00bc725767471b55237ce4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf61581890867e8fa3bf1a8bd91cedd2", "guid": "bfdfe7dc352907fc980b868725387e981b0a2dee5561b40a9852b25b414e2879"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d0764e1abbb009d476a033aacf90b4d", "guid": "bfdfe7dc352907fc980b868725387e98b36e0c7ee2b459807d10ad05be5dd76c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982452a6b1c9491171e462c4532b10fa4d", "guid": "bfdfe7dc352907fc980b868725387e9859cec75bd7de3e93478e8d5d3dfeda4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eeeb5790a8a3979a1b2210d0cea1fcc1", "guid": "bfdfe7dc352907fc980b868725387e98ca43acc4f8dec3b4add0f2d7126bff29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f660caa125cc400efd80ac4176fc3c2", "guid": "bfdfe7dc352907fc980b868725387e98d09cbe1f33309b1c886cf2f6f04d1484"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856fa084b068c1b7cf2fa6f247a21ed55", "guid": "bfdfe7dc352907fc980b868725387e98dc5d05617d98dfcb1bf4354c3bc6bf8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837b1a74cdbde4574a33f292437aa7459", "guid": "bfdfe7dc352907fc980b868725387e984aa1a3c7dec78a8cca8841d05a8ef5b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb645155551d20d27ba7ed6f513ff263", "guid": "bfdfe7dc352907fc980b868725387e983ba0a7fafb3f9170a47975473bb2edef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878bafe7b0103897d63ef61dabda36ed4", "guid": "bfdfe7dc352907fc980b868725387e98aa2b6fd135fd3edbb7a845d28f39245f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832fbb0eae6dc33d430fb1d9625711d51", "guid": "bfdfe7dc352907fc980b868725387e98d50bc6a27053d9520449478365136868"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd718ce56425e211a92ebf028f656f53", "guid": "bfdfe7dc352907fc980b868725387e986da16840ca868782d01abbd24aebdaed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e240bf87529609cffcd8552c6061f83c", "guid": "bfdfe7dc352907fc980b868725387e9817adff958bd7368504ef326bf61077c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ff6acae8c678e01f60f314dde9327a8", "guid": "bfdfe7dc352907fc980b868725387e9850fea11ba8f90ab0399505b4f46ee8e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec05d76b12dba16bfb86de31d99670b0", "guid": "bfdfe7dc352907fc980b868725387e98883349bc3ec91ed545d085067420790e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b802f88f97ccad0e91812363bea1d707", "guid": "bfdfe7dc352907fc980b868725387e986ec2a1bbb9f3230ad25f3f2c5b3833ab"}], "guid": "bfdfe7dc352907fc980b868725387e98ba495e411a3b4b9c874120f486ed7a36", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e98d0cf71094985ddabe24c891296df1448"}], "guid": "bfdfe7dc352907fc980b868725387e9831502d2d958fe5ab55d0adebcd40072e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9846f6ec6a9cea0287eb9e8fbcb1797fee", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98476748a04e286297f71e43b48bdd2c95", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}