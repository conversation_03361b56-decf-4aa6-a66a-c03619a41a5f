{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982f75f154529a2b128038fd68e29deca5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Stripe/Stripe-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Stripe/Stripe-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Stripe/Stripe.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Stripe", "PRODUCT_NAME": "Stripe", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986db2ea854a7e78b71f8ad67ced9c2c78", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e4c3bb0c092a81c04a4c52c06357f419", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Stripe/Stripe-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Stripe/Stripe-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Stripe/Stripe.modulemap", "PRODUCT_MODULE_NAME": "Stripe", "PRODUCT_NAME": "Stripe", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bba3dbbf5909b6c365b92afe82a664a7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e4c3bb0c092a81c04a4c52c06357f419", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Stripe/Stripe-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Stripe/Stripe-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Stripe/Stripe.modulemap", "PRODUCT_MODULE_NAME": "Stripe", "PRODUCT_NAME": "Stripe", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802c602ae5b04d89b0afde8d161528636", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986cfafc156d8223300c1a8c43e1c34562", "guid": "bfdfe7dc352907fc980b868725387e98e60478a908656fd30cceef25f7966043", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989d5f60522d24d815f8bac57330835049", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a0e8065b62e975cf33c86f6ebfda7fe9", "guid": "bfdfe7dc352907fc980b868725387e987ce32ef30ad9031e57e3a90281d47dea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b606273049f9ac08eeb8d74cb4534b8", "guid": "bfdfe7dc352907fc980b868725387e9811b28fb845289c82c0f619e0db8ae9ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2e62dd04db5c82eac3898e0579988f9", "guid": "bfdfe7dc352907fc980b868725387e984d2c1b00c3b4d190d3c05d4280981a29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b671f63e80c4948ee06172bd6527ced", "guid": "bfdfe7dc352907fc980b868725387e98be6601aa0ad0914fcf51a9ac8d0712b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca189d82689d6872f54f0dd80e92552c", "guid": "bfdfe7dc352907fc980b868725387e98d8042e106ef228b2b28f94fca6a75295"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98879bba92339d44d9a532547b902bf972", "guid": "bfdfe7dc352907fc980b868725387e98002e9aef018bfc327f0ad470c825a7ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee21f177fee848680d8634b4a93c5bd", "guid": "bfdfe7dc352907fc980b868725387e98bf7dc0cb1eec972ad2b0a665d34ed716"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c59b50559c7ef286410a9b112c4ae3dd", "guid": "bfdfe7dc352907fc980b868725387e98ef85e6873a1543729c82171de4b27cdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983be1e0be65f8363665995dff30c31c7c", "guid": "bfdfe7dc352907fc980b868725387e98c70df63b97253479d5645f2d59085956"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e8c73bd5fe9d04ca3a629ac118c44c0", "guid": "bfdfe7dc352907fc980b868725387e985f91b84abd6f27eb477c29c0d25ad2be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd38c75521986e7100132d77cba829e8", "guid": "bfdfe7dc352907fc980b868725387e986a60994f4657d901f0ae9df416419b92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b928f70305a1e52c8b67cd6c3592eba2", "guid": "bfdfe7dc352907fc980b868725387e982ff2e63ccc10e118a6212efef27646e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989772532ffefd690085d94e53f2b6bf48", "guid": "bfdfe7dc352907fc980b868725387e984a926dafdd6485eae82919f3877230dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d3b42528b83e28f281795d0be754719", "guid": "bfdfe7dc352907fc980b868725387e98276de981e8d3d324afbb575c44213d9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dd0e6ac0ed9a716526ab34bf2e7cfc3", "guid": "bfdfe7dc352907fc980b868725387e988ac6b6ef2f54018f88cdc056090436c7"}], "guid": "bfdfe7dc352907fc980b868725387e98bbbffd48feddd511d61f64f49aa8f55c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98de01621e36ec1d33b31c5ff13476e16a", "guid": "bfdfe7dc352907fc980b868725387e98382a4b2ce8f8daceffead4b794906824"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807230c5ef5965f2ebd9b4a00c2138148", "guid": "bfdfe7dc352907fc980b868725387e987eaa74171be29e301f777c2052703ff4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e98f8ca196843f037056a08fea10ab0f22c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860406162a67f0e7a626f0819bd34c8f4", "guid": "bfdfe7dc352907fc980b868725387e98daac9bf7d9729dcd6ead04446b641487"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d05a97faeb775352b784fcd3a04da5fd", "guid": "bfdfe7dc352907fc980b868725387e983e0bd883fa4a83f1a5ae88e1d8b2e3f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880a17b68e7b3f6ff442aa03cc954387a", "guid": "bfdfe7dc352907fc980b868725387e981822aa35b816c10304b349ac384b739f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98394eff0a6c3a70635f033838e34f69ef", "guid": "bfdfe7dc352907fc980b868725387e98b8036d64176af36583be07674f28eb9b"}], "guid": "bfdfe7dc352907fc980b868725387e981adf6d09c6cfa0ce5ab444f352acb52e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c79ae6a1a307abe093e5d083c5bdeb20", "targetReference": "bfdfe7dc352907fc980b868725387e98e3c8c4fe52fbe53a296306feca7852dc"}], "guid": "bfdfe7dc352907fc980b868725387e98fa7d747ba40a6a6febb3fdf921e4405c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e3c8c4fe52fbe53a296306feca7852dc", "name": "Stripe-StripeBundle"}, {"guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay"}, {"guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98caf0f30362a7eaf9b8b7f5ba71771d54", "name": "StripePayments"}, {"guid": "bfdfe7dc352907fc980b868725387e98bfacf038ceaf928d957d7e7abcab2e3b", "name": "StripePaymentsUI"}, {"guid": "bfdfe7dc352907fc980b868725387e98f17b608a8faeb01ef6ec76d52489fd0b", "name": "StripeUICore"}], "guid": "bfdfe7dc352907fc980b868725387e9802b8a2060b8f4c4f36a50487027e7bca", "name": "Stripe", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e987baf43f4f9f06b41bc08abcf722bdf59", "name": "Stripe.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}