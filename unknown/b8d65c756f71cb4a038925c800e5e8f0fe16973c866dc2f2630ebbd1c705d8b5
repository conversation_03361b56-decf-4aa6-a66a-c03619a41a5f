{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984e38f4110519527d7f738746c0ecbf37", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ba6fd30a2d2c0667ddeeb471b87f3c67", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a622af17fefa323161607561197fc5f8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985872c6b49f1d92292872024b383071ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a622af17fefa323161607561197fc5f8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869811edde2fbec4b998cd7c872ea4b10", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d8216687e8e8e447a2a70aca78c16116", "guid": "bfdfe7dc352907fc980b868725387e98cb0473bc902eb0799d7d77612923553c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837f33fa33caa460e7f1025929117a655", "guid": "bfdfe7dc352907fc980b868725387e98d426227e5621e47baa8f80f48b2f45e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a46c78f006d290973e1f8ec2dd328f4", "guid": "bfdfe7dc352907fc980b868725387e9817ee12b22a7e8b07f9fffec9f10bcd8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876c3b80b959cf311765db2083ab246da", "guid": "bfdfe7dc352907fc980b868725387e986645d1191fa5e22caa65dbb979b810d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820d96b2e0c2dac9076d760ac7a8727d6", "guid": "bfdfe7dc352907fc980b868725387e987a8b38dbd8045f96d96aa5559fdd1da7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b234998cc5baf9441bde457e549098c5", "guid": "bfdfe7dc352907fc980b868725387e9863eb21c94d2d16f7cb7f8a5318b53842", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825e59c350b6c9087838756d36ed69620", "guid": "bfdfe7dc352907fc980b868725387e980d589df9f09dd318b3d1f2ff568b7726", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f18247c53585e36c28de9e603bfcdaad", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae1c2f51ff86aa22ce2bbe038c3b254e", "guid": "bfdfe7dc352907fc980b868725387e98fcc41cd4e9926a57b67b04916a1fad3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbc3c9b6652e5f8f12aeceba01b7c954", "guid": "bfdfe7dc352907fc980b868725387e98eae6fac7b9e5cb36a7ffc97b2b35beb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98949c40d670c9967f67c6c9ee0aca70bd", "guid": "bfdfe7dc352907fc980b868725387e98d0531c2e38539705dbd451bf6d94b4e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98943d7eb32e386317ce79de9d8c4c7ce2", "guid": "bfdfe7dc352907fc980b868725387e982937bb12844041b776fd603176751613", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985096faa36bcc2801b8c3df0e01fd1aa2", "guid": "bfdfe7dc352907fc980b868725387e9882da7d975ece2d2bcb7b8170851af42a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f0d4647788213a9daff6323b89a26ed", "guid": "bfdfe7dc352907fc980b868725387e98196cc9cc4cac503e1ae42e78e786c600"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c76619394138829f014b86a15f46fd6", "guid": "bfdfe7dc352907fc980b868725387e98337fc7f2773f6e74a568ef73b19d223a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856c2fb79dc8bac3a73dc3c762098df6e", "guid": "bfdfe7dc352907fc980b868725387e981e80282cd55cc4cca137aea9d8c23947"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98932f4562210888e453e26940273dd938", "guid": "bfdfe7dc352907fc980b868725387e98539adec4c5fa635ec6ce4db68bb7215d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98190b740efae0ab9290e844c52db17ba7", "guid": "bfdfe7dc352907fc980b868725387e989291ec9eb04d627d89c3ff9c55407196"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865b0a4e234ab7afc0b9a7d9fc53937e0", "guid": "bfdfe7dc352907fc980b868725387e98302030a1bbd39b0abfdda494f21dc5ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c610c6d61395cf65285727bfb005fd46", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d3ed8c43311db0b443eac28309b0414", "guid": "bfdfe7dc352907fc980b868725387e981d7c5712bebdfbd6b4683f02a2de6688"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98296ed1d907400d60425c9b3d3c34101f", "guid": "bfdfe7dc352907fc980b868725387e982df855cfdae0032734245e207c5e1433"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b98f2c75aaf5179bcccd1f32b54f9e16", "guid": "bfdfe7dc352907fc980b868725387e98f7fa9614d1df44eaf393a5dde3c52e8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858847956445ba7da1e3ba3d5d4026a83", "guid": "bfdfe7dc352907fc980b868725387e98f94296ce02e0d6b9c928bd6de468d3d7"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}