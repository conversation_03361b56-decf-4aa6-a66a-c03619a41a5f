{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d890a36cb773a859901803964808ba24", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98de94b351f9ae41bdaa9e0700e6d3c09a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98871d2f201440ed03669c5d67dc6cac85", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e9f438cc5563edcfb8e8fc51e26cd12d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98871d2f201440ed03669c5d67dc6cac85", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b48011406bc205a50e4a6b73e20dc5bc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9837bd703a498772b2baaf4cf384967a56", "guid": "bfdfe7dc352907fc980b868725387e989064b5e9f251b303ab7589a2bcaa5bce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812a42daea4a9c4071e792f7307398b52", "guid": "bfdfe7dc352907fc980b868725387e98d3ea3df2812377ffe800040a0761acfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b89ae8359c7d66b7f339533856c0067", "guid": "bfdfe7dc352907fc980b868725387e98105bbec208a7126b141353dba69cebfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7e3e7787c054866629d639623aedad1", "guid": "bfdfe7dc352907fc980b868725387e98749265da5d7d45095be827d330762445"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a82c2012cfd0f23bbd72c400fa9b0b36", "guid": "bfdfe7dc352907fc980b868725387e98e8b4ad6000f12d89e30409189f4ab0b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98400c5c8be3f794da7e7a8f82322779ed", "guid": "bfdfe7dc352907fc980b868725387e98bae5f8469c4477a80e4f97a34897745c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980af8c7b9fea2df06062d98539f016a6f", "guid": "bfdfe7dc352907fc980b868725387e98930ea086c858e20abaee5576d61f95c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9721890984b51024993180ebdad23cd", "guid": "bfdfe7dc352907fc980b868725387e980e265e676e15d42a6c820613199f5d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98512bb9522609a8757143b4be0b33559e", "guid": "bfdfe7dc352907fc980b868725387e98479981cacb71d23c50e5bccd03749c28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ba973f9eb7de021e5c1e0ffe1c71727", "guid": "bfdfe7dc352907fc980b868725387e98e4900e7255edc6df8b542b04bc5e3da6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885126eb4707fd99be030841d27398f5a", "guid": "bfdfe7dc352907fc980b868725387e981f9f797aebf2adaf3582f238900e5d70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861771164fd93d9a5436cb6c80971a4d2", "guid": "bfdfe7dc352907fc980b868725387e9885e518fe7afe5ba55f62ad0b4a5eeefc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a58c629a63badd3ac1e2aec9823d439a", "guid": "bfdfe7dc352907fc980b868725387e98a614ce9881c2b3101a612a10b3acd80a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbdc37d2f60e1c38dbdfb4b1bb2b3585", "guid": "bfdfe7dc352907fc980b868725387e9818988c10c15b05d38d3095e88327aeab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855ed520a5ae2066fa4dcf66d9aa8a885", "guid": "bfdfe7dc352907fc980b868725387e98a4a6f4260be2f4add764565ceafd4e93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ad3ced35e8eaf3a7ef88a61c9ff2474", "guid": "bfdfe7dc352907fc980b868725387e981cca6e9f0c199f11ae5759e8a9b7fb29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b61124de9ebd7942f07bbb22ea3d5c1", "guid": "bfdfe7dc352907fc980b868725387e982a2307776470bf101db66c2c57c192a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba2f9462508753bbc5b4d5f781d3087d", "guid": "bfdfe7dc352907fc980b868725387e98aac265160e133f7a8c36e71edf3bb7c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98913925df22e1af7cb2a98e40056afde7", "guid": "bfdfe7dc352907fc980b868725387e98d0533def47572ee066c61e5d2d7df976", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad0546012916a31b999ec2d3c4cca78e", "guid": "bfdfe7dc352907fc980b868725387e98ca994c57d544332da907bbe273122dca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f913f15608b934e8a53ccfae724ac308", "guid": "bfdfe7dc352907fc980b868725387e98d543f19e7e6175dbfab3501ba6576170", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f562d8d40b7f43ae85085e0be8c98597", "guid": "bfdfe7dc352907fc980b868725387e984c2b90722ca41950f48d518b036269a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989674932e4d02de6e3be0d3806b884381", "guid": "bfdfe7dc352907fc980b868725387e983bbf4ebda4962aafda6e42dd9fcc59f3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9898702afba55f5b18257fe5c19f384b9c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988ed7191e1d72f66a46ef8d0142e8c591", "guid": "bfdfe7dc352907fc980b868725387e983356a86983c0ee98d4ce1168c05d6b35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98966e831c120a1b776088bcf864d6f34c", "guid": "bfdfe7dc352907fc980b868725387e989eeeb38903b55c3c4705e0d58cbca48c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987028b9bb6a09e586b6a9f098bc07edee", "guid": "bfdfe7dc352907fc980b868725387e98e642be9bc9351168be7ec6c188cdf4ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833c47f480fa7af667f3b5cd38f9e67f7", "guid": "bfdfe7dc352907fc980b868725387e98e5469b35105191c5a0c8ce728b05f359"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882ee1ebb161cb3ad1d44bd592672b2a9", "guid": "bfdfe7dc352907fc980b868725387e9820474a8a95dbef0540a07ba8b27e2886"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1d50b91207589e0ee6238054fc46acc", "guid": "bfdfe7dc352907fc980b868725387e987e666527158aefec64b2022810fe2dd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3afe50a327efe66bc6939dca8cfa6ad", "guid": "bfdfe7dc352907fc980b868725387e985b5154ea178385f67cfea49f3ae7c5bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987153b6370ac293230e05919220fcc680", "guid": "bfdfe7dc352907fc980b868725387e989768b687482fd6cb3ec60f0dba777a22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a599b7818d590d1225c409e8497b3205", "guid": "bfdfe7dc352907fc980b868725387e983b133bd7fdee56028228c4d278ece086"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a93c01ee708eb73643dff880788c7cfd", "guid": "bfdfe7dc352907fc980b868725387e9858aba165bc8858ae01e68c6792f4dfcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb3c0b80517912d8c27dd2c904be0431", "guid": "bfdfe7dc352907fc980b868725387e981a14d6b9b4c62501f6f4b4e56512e7b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fbb790fb1e98650d60dbdb3426ffa13", "guid": "bfdfe7dc352907fc980b868725387e9851be8a7caa743423f30e092e1bb0b5e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f51937399c1eb8ea87207b3716685f6", "guid": "bfdfe7dc352907fc980b868725387e980df7915a994e9fb3a241b0660d512819"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5c5d1ccfca348871e60e0b0bc855021", "guid": "bfdfe7dc352907fc980b868725387e9876543ffe81007c033e1df5ea9370a70e"}], "guid": "bfdfe7dc352907fc980b868725387e9870891974e101d06a2d23ecf0ad29b604", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e98f73d37134e09d79dc6fa215467850b75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880a17b68e7b3f6ff442aa03cc954387a", "guid": "bfdfe7dc352907fc980b868725387e982a537209a6032eb47538ebd1a20720e2"}], "guid": "bfdfe7dc352907fc980b868725387e9822e8a87485af076130c0f79ab2f04f03", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98660bcce7605c925ad560e9b8011bb2ea", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98e7c30100d52d3c39a74aee4d3fbae453", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}