{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5a88a8393321764fe60f01d4e418ffb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b59bf331f17ebab38be3105502a2e91b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b59bf331f17ebab38be3105502a2e91b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fed4fa30dcd5957e404f04897bffb3f9", "guid": "bfdfe7dc352907fc980b868725387e98cae00bb5b44801c71ec3a4520a92d03e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2bcadceaa2f20d057862cec0ea54deb", "guid": "bfdfe7dc352907fc980b868725387e98f5d2e147aaa327ce610fccde99e61780", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989adb40b33de31916d9b86cc1290bdf93", "guid": "bfdfe7dc352907fc980b868725387e984a0f0f2bd2b4b374bdcc8a5c1c7d88d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5e0e779390ce1f749f87e0dc8a379eb", "guid": "bfdfe7dc352907fc980b868725387e983bf37a07874ca81a305d7ce3433796a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98456a18e033f3b32e7a89608f38fd6611", "guid": "bfdfe7dc352907fc980b868725387e9838d7fea7d9e670322fcacd17c565a0d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2d9cdb7ebb5370ee0e1a0b3f13442e4", "guid": "bfdfe7dc352907fc980b868725387e98eb6a274c8e2b3957ca9ff5eaa149ecc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b60b50d1d0441c59a196353194237249", "guid": "bfdfe7dc352907fc980b868725387e984e4608bf2e65ff1dfc035e57803ce074", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b81d6f0c1f54db809d82737da4f924f5", "guid": "bfdfe7dc352907fc980b868725387e98ee95ff9911a7b89a95ac4514ee643c4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df80dd7ee5abb3f1ae5bcdd24475c995", "guid": "bfdfe7dc352907fc980b868725387e98f61d3c84914ec837e1c27fddf1c8e055", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e865c5b15b213c263626bb529bf7722", "guid": "bfdfe7dc352907fc980b868725387e98de9c3dab1e0e36e466cfdb7fb62eaa37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c37e4cd2cea216f40d4c615fc538f1ec", "guid": "bfdfe7dc352907fc980b868725387e98cd465e3cf789a98b784de6a381c58dad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800670942a3844c90e138348c2cfbffaa", "guid": "bfdfe7dc352907fc980b868725387e98e36e9c09557410d273c4c514cd97dfed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcc72995a2d645ccfa420d8352aac229", "guid": "bfdfe7dc352907fc980b868725387e98d0addf7ba217bf14de248264f8b43e35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828e88f25b4200827e17e5d0243409210", "guid": "bfdfe7dc352907fc980b868725387e98ac4b16de3725db9629f2086436b05a35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbc58712559be22c1c918404bb0e3fb6", "guid": "bfdfe7dc352907fc980b868725387e987b1063eb4f28ece0c1cb73de4596730c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f613e8af26290c6edb34ec44b7b7aff0", "guid": "bfdfe7dc352907fc980b868725387e98b89618d6bac33b1391895bbbc51923fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf1e10543feca7c5b83ed60e35e71fe0", "guid": "bfdfe7dc352907fc980b868725387e9850b1617d3d45d5e3c0373de93dfc8be8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98866e094ca2d29d155787169c6adf4923", "guid": "bfdfe7dc352907fc980b868725387e9829ffdc174adc92ae3fbb075c5918f7b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98647d2d4489b2b680dea790e0e7b162d0", "guid": "bfdfe7dc352907fc980b868725387e98266968e9dadd9363297501c83f4ac0d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b3c6904a609e0a055fdf9eb02f930d5", "guid": "bfdfe7dc352907fc980b868725387e9893009a85b440cef40bbbea8dd972ba1c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98448ed483e3450e962c943bb2a5428c6e", "guid": "bfdfe7dc352907fc980b868725387e98453517e3b608c8285dff3129c23bc2c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98913d63ddcb22754fdd566de39d5d7af4", "guid": "bfdfe7dc352907fc980b868725387e98bc4e588e1fbb985c3073f4237d973f01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982578d3aa5029c2442d7be8909edcefa9", "guid": "bfdfe7dc352907fc980b868725387e987b44567989d99000582eeafccefc6c5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c08ecd4c76ea108695af02358aa45a7c", "guid": "bfdfe7dc352907fc980b868725387e98e5d1e1506ea4a97edfa7fcf441b16786"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98247ab3b3b9610b80630876548178be32", "guid": "bfdfe7dc352907fc980b868725387e98bafbe9b7bd18750b9a73b2be5b1a130f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5944f3562cbaac614d0e9ef18f88e67", "guid": "bfdfe7dc352907fc980b868725387e980ac203fd4a86456c8e8d132afdd1f89c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e86bda5fe751eb52ac448cced4c6665", "guid": "bfdfe7dc352907fc980b868725387e98317ad73bcf6417d72fb367c1267e0d29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805b5c33ce0583b96b87bf08f553f8cf8", "guid": "bfdfe7dc352907fc980b868725387e9865a533b16f7e49ade5698d1e462473df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879e7aaa637bf0a46e97e88905972558d", "guid": "bfdfe7dc352907fc980b868725387e985f409c21c7a33d2eee16ada8f61db6a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835ca4a2f57252e2730b0a5737b8b7747", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dfa4c17a0852d3e54f76a0f258f76c5", "guid": "bfdfe7dc352907fc980b868725387e981ed0ae5ad469a52fb5a95fbc502e6a79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7181670985bb954d1282c9d367e7b74", "guid": "bfdfe7dc352907fc980b868725387e9864d5435733201948c0e24ae6fcb0bcdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc928d05b2ef0d25fbed849f6d94404c", "guid": "bfdfe7dc352907fc980b868725387e981555040240ecde663931b54a61d8186a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98449d4dc8b0a815af46efe0b95f6f1a57", "guid": "bfdfe7dc352907fc980b868725387e984df4cc237d84606f286da37cabb29b84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0d4074fffe06425dd4985bd7a9e9d18", "guid": "bfdfe7dc352907fc980b868725387e984a82ece08a7d4f1e09b0a7d7cfa1fff9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b8b7d9000012755331c96cfd9ae4038", "guid": "bfdfe7dc352907fc980b868725387e98d5ffe14cb17c642f48ce2d68bbf6b40e"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}