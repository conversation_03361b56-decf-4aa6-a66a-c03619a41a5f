{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981943b33b60a7fdec55ee44989aabefea", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cd21452a48e306f70aa69e5f093db8ea", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98951eaa19b31420cc743d7b7b90807a25", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9858011960663164fcc99796bc4005f6d9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98951eaa19b31420cc743d7b7b90807a25", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ea283718a29e815815a1b1917300e8ab", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dfc9fe2021ff4e69281963aa6d592786", "guid": "bfdfe7dc352907fc980b868725387e980cd825b350774b105ec9bebdd4eb8668", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c6a8858c71d876bb44b8eb076622ac96", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980c16c9a99458dd51e27bbda30ac94d0e", "guid": "bfdfe7dc352907fc980b868725387e985027297aa6cda873d56ed4367fd9d637"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d142f6b01f2ecc0f4361b7c90428026", "guid": "bfdfe7dc352907fc980b868725387e98330de1f9c13a8b436d9448767e95b1b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c6af20bb8aea93e8dbcc5bdff90c4fc", "guid": "bfdfe7dc352907fc980b868725387e98a714bf0ac91461f3a1936dad9c2a7a6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c353b74ddff533394eb2149fae4b6a16", "guid": "bfdfe7dc352907fc980b868725387e98f0489c501e9870c68dce085b98675fd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98545f755bc854b376c721c5bc243e9937", "guid": "bfdfe7dc352907fc980b868725387e98a78564df2da3b6964fae9e2bacf86592"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988263f03f3338632e1dde55b4bfeb41ec", "guid": "bfdfe7dc352907fc980b868725387e98e53cfc16277b741486bdeba676c06434"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985438ed9f6f1beaf22e5aa661aa8cdf78", "guid": "bfdfe7dc352907fc980b868725387e980e80ab8c4e2f42b6d3bf668250c9bae1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6bfb9ca2a75545befcd78472e2695f2", "guid": "bfdfe7dc352907fc980b868725387e987c3af33e3bcf5a4adf209f455899a5b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf4c0d7dac592514cba6ff5b8398003c", "guid": "bfdfe7dc352907fc980b868725387e9818efb14cb07cc074f3f6023ff0167e40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819ae0d81b619b0e4881c9b575012659d", "guid": "bfdfe7dc352907fc980b868725387e9879b5ff0d8f9fe3373415843f85fbde3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98660f9728ad5f38a009e7257326577a65", "guid": "bfdfe7dc352907fc980b868725387e98b7aab6aca6798221a622513c5dbb337e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98974e15b14de28cd552ed4fa6f4ce4901", "guid": "bfdfe7dc352907fc980b868725387e98a0594ff06652d03f6abd442e6ccdf2f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4bf5db8e6fe0ef1a5d8b16e030dcfa4", "guid": "bfdfe7dc352907fc980b868725387e98184ab52f60026b14bb65f291a6de1f59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d8018fca81ca3bca7129e426fa13a25", "guid": "bfdfe7dc352907fc980b868725387e98e46afee690fd46b1c83380f47b9d2f6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba8cc2e4a2d2b5415f8c0b089611c221", "guid": "bfdfe7dc352907fc980b868725387e980dfab00d4a0129f1a1d7f12b73fed615"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867663fed09db84fbd0142eb7f4903083", "guid": "bfdfe7dc352907fc980b868725387e98a3fd1e8015892670c66621a8ec12435c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5544944ae936e56185b512ff755ba75", "guid": "bfdfe7dc352907fc980b868725387e982e55050edd8b01791f032c26f9102e28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a7e6de9e182ae4de5471bb5a9b43b17", "guid": "bfdfe7dc352907fc980b868725387e985a1993b21b945c4887f24a0edf596ae0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d804120d9503ed9f3c5e8f981f9155d1", "guid": "bfdfe7dc352907fc980b868725387e989d3662c21072ad32b75ac3ca8fc52baa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98955cc166933b9984f536d80dcb2dd165", "guid": "bfdfe7dc352907fc980b868725387e98a49fd935448f0fdad73ebadc6c50bef9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad29564c15f78bcc435f6398eb09442d", "guid": "bfdfe7dc352907fc980b868725387e983400c5af5ba58124cfabc164ed4160cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9748260cc4cb3e4d3e57c4bb0d045d3", "guid": "bfdfe7dc352907fc980b868725387e98f6b60c6e6c1b2bdf4902c177bed3025e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800a9e96b25c003b5f0bf6ba1c2919db9", "guid": "bfdfe7dc352907fc980b868725387e985ed3b6e1e6fd112eaede3ff953bcd0f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab7bb3f2f559d64d68b88365b42ebada", "guid": "bfdfe7dc352907fc980b868725387e98c826bb1273b8b3f8da4ba1386babc82a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8fe52fae31f038138db6bae0d58b780", "guid": "bfdfe7dc352907fc980b868725387e982352b36c0eea4d651b30662b7842e654"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddf35d0877b25da9234bea27c9d50771", "guid": "bfdfe7dc352907fc980b868725387e98de285b0bb99211234489a0d53bbd82e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc0c78656cd3c4118a15f10c14584ab4", "guid": "bfdfe7dc352907fc980b868725387e985d22f6f920ab226404a77a68501393b8"}], "guid": "bfdfe7dc352907fc980b868725387e989be7525af9f8a54e056940bfda8f32c7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98de01621e36ec1d33b31c5ff13476e16a", "guid": "bfdfe7dc352907fc980b868725387e98f6c8815e8a76571220c3a05bb3f8dea4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807230c5ef5965f2ebd9b4a00c2138148", "guid": "bfdfe7dc352907fc980b868725387e98e7fd666e9bbfc839e83087543c8d5127"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e98ccbbe47b3a2881bf9ecab121dea31655"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860406162a67f0e7a626f0819bd34c8f4", "guid": "bfdfe7dc352907fc980b868725387e98fb52e261399f480cc04e302567b6db08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d05a97faeb775352b784fcd3a04da5fd", "guid": "bfdfe7dc352907fc980b868725387e980bbfbde320fe5ed725c22d7ffad9de54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98394eff0a6c3a70635f033838e34f69ef", "guid": "bfdfe7dc352907fc980b868725387e98ffc32b2906b0a9b8536f0f52088d2ef7"}], "guid": "bfdfe7dc352907fc980b868725387e98766c010edd6722bd2e3b403eb71dd4e4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e982b41a54b923a7519360c44a9a20e486e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore"}], "guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e48a873b6e297ebc2a87f23eb2fd0723", "name": "StripeApplePay.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}