{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98106925b8f2a4f5b305001d2d9bf900b3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7a99df6e6a4873f55b3ad33955c73d7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984af4a1c1fea6425197fd8a8dab7f08c2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98961e7c9baafe5b4e26a5c59132394731", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984af4a1c1fea6425197fd8a8dab7f08c2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5e2f4ec47caf1c35a822cf8398efdf7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984361cc6441ec6501b2c028a9bfeebf96", "guid": "bfdfe7dc352907fc980b868725387e983a7f56a8bda7f4a32b582f3e3543ec6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa1f767c21c18deb5acb099a662ace53", "guid": "bfdfe7dc352907fc980b868725387e98eefa1fbb52d5820fb2f74287e89fa5be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98619cf14f0f44f4c43f4c73b887179c69", "guid": "bfdfe7dc352907fc980b868725387e98731bf14971647468f98880f5ab2ecebd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98408890c03417aa833fd94985ff096e74", "guid": "bfdfe7dc352907fc980b868725387e985188a1987f7cf2a34653743fc646f39e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d32d370e200da0e711ca9096007c58b", "guid": "bfdfe7dc352907fc980b868725387e988f117cd19bea713411f7d886e3ee57f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98279f4e327e4622bbb0ec3b842e3ae52e", "guid": "bfdfe7dc352907fc980b868725387e98f7013b8d08955f03cd9ad217b6575ec4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98370e35277c85cfe496fec1cd4cf9dad7", "guid": "bfdfe7dc352907fc980b868725387e98c3717fce461d8c3c727f6ab1e1ef8dd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828711948a0eeb2be7f52f7da82104dda", "guid": "bfdfe7dc352907fc980b868725387e98971d704d92f697bb1c3431227aeca581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d2ffaa8ff673adde9cdb35c4f39a3bd", "guid": "bfdfe7dc352907fc980b868725387e987fb0a97b5514da31c86f2442dd9c5c3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98550f7d64bb7f076176f88335de088c60", "guid": "bfdfe7dc352907fc980b868725387e98557eecafb08f3d4c91d7a27a04cbc98f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98257910cdf4c3ea334a554d19bf21ee04", "guid": "bfdfe7dc352907fc980b868725387e9831a692eeee4b67a14159ea381967273a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830d76d9660ded14c24da44647147c54f", "guid": "bfdfe7dc352907fc980b868725387e986a533507042bc1b488b8d411b4e467dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd52e73e773b1010a92907bdd095d4cd", "guid": "bfdfe7dc352907fc980b868725387e98ae7871cec43b2a430799debd34abfca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f657b03f41a9e5e64a6aacb1f95e2d2d", "guid": "bfdfe7dc352907fc980b868725387e9843848d640b95d66ae7b0609d40822a18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888d56d4f1a1a1fc6c426bc7927b2dd17", "guid": "bfdfe7dc352907fc980b868725387e985df37ff259fbebfcef0385a6ec8da199"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c84d1838cc75b41f4d1935761538aa8", "guid": "bfdfe7dc352907fc980b868725387e98f8b9a4332a9d35d417da9e60a68e1e5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c8c46f14f9ba3c4018094d1b8eefe45", "guid": "bfdfe7dc352907fc980b868725387e987016ef18ef8e69d7bc59d3c64c1062eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fdf387f2724fb9655a6d41c40f4383f", "guid": "bfdfe7dc352907fc980b868725387e98638fdbf659b7db656daaabefeb0fa1e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838149d9038dfb70e5d2d8321285595fa", "guid": "bfdfe7dc352907fc980b868725387e982366610e765f0b5c67b62d5d700c6ccd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842d5ac70b45143629d678e16d4f7b4cd", "guid": "bfdfe7dc352907fc980b868725387e98201a1ca6306b21611dc8f609d62d8e10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba13c70955594855eb07eb3f99ffac72", "guid": "bfdfe7dc352907fc980b868725387e9805d1768fe792537334f391c041c31f35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9063a2b0c2655e3e3ad4fec3136b6e1", "guid": "bfdfe7dc352907fc980b868725387e981ca2893936b4e0ab83db00de99e3fe4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c2562ba7bfc44c3dbbbf668d7c437c6", "guid": "bfdfe7dc352907fc980b868725387e98df05095938a6e6a95d183f2debb7eeca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eef6ea8107f391d8d140a111c36e2fd2", "guid": "bfdfe7dc352907fc980b868725387e988f3992c3226d3fffed029e44a32d785f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891e73ab31ddaa8970ae34d8006c22287", "guid": "bfdfe7dc352907fc980b868725387e98325b799f84feca7cce3ae0c158b3d6d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0c7d44f0563fd5537093cbe15b613eb", "guid": "bfdfe7dc352907fc980b868725387e98d73be45a5c15dbc80c5e29415079a21a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a431de238be897cf1ebb6e6da81eb9d1", "guid": "bfdfe7dc352907fc980b868725387e9895e853d89407c6a61308b8ed634f6c83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd0ed4aa39c5280d541a8891de269aee", "guid": "bfdfe7dc352907fc980b868725387e983f07250a53c4dcc69dfbcf67ebdc325b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837b55652ab6b63a8dab48cce9caabb9c", "guid": "bfdfe7dc352907fc980b868725387e987481fc11365e35e19c58f07f6f4b732c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98625ad7ef1b647f9a08b84f9f46dcf7b5", "guid": "bfdfe7dc352907fc980b868725387e985b6c7b86e2e3ddab6f8772c5362a6fba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee18af7774e640b9cebdcc381510e91", "guid": "bfdfe7dc352907fc980b868725387e988919c6af1499779a90e7598a5e84fabe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c85f4b0b7a5c2388e7a0fc6e64d8574a", "guid": "bfdfe7dc352907fc980b868725387e98d6d0fe9c6b6c404e0d62b0cbcd05dfdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a01e8e556783abe16401581a1e41de5", "guid": "bfdfe7dc352907fc980b868725387e9810d41d30cf3c158f9509077744b70505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf522c1d7fea932d0a6e1dd1847c54bf", "guid": "bfdfe7dc352907fc980b868725387e9865d6cb4f29a80f2f2ce4e60e0090a0bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ab26da7b0eb1fe1918b89406e86d315", "guid": "bfdfe7dc352907fc980b868725387e98ea323f1a49908e40b0b598e61db59b71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98416fa79e40614c77b25833ec2e4e1bc3", "guid": "bfdfe7dc352907fc980b868725387e9866a81d67491e84a3ef0e7b3befdbdaba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0984d3f1afa77756bfa2e5e85b2afa3", "guid": "bfdfe7dc352907fc980b868725387e98f9a04fb7607f394362f9bff3a632b600", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b56cd1d533f104d1d92d7714314a7d86", "guid": "bfdfe7dc352907fc980b868725387e98c1b9cbe39894801763b170085b67c5ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da047e7f50a6a99e3c799bdda0056f7b", "guid": "bfdfe7dc352907fc980b868725387e984e87e4b3e6b500cceaa46ede0ec13dfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcf2fadd06f216255864e70d785d788b", "guid": "bfdfe7dc352907fc980b868725387e9804e24ee381ecd94ecdaeb870ed0114ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e58a00e099982f4ae5a48cffb5c7df73", "guid": "bfdfe7dc352907fc980b868725387e987e8729dc670ba4e8dc2e7ab782a73fb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2bf67732ef3bee964ec653f36a7ae1c", "guid": "bfdfe7dc352907fc980b868725387e980673b86a47f0fe84859c2176137bfbd7"}], "guid": "bfdfe7dc352907fc980b868725387e98dc3f6c017f50c3291e26862a62071ad6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9803f0ee8fca902fb55b3904c64e84ddf7", "guid": "bfdfe7dc352907fc980b868725387e981251f36c01e0bb324ca1cf7a8d9bddb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f90164641e75cf61c9de757b557d1b8a", "guid": "bfdfe7dc352907fc980b868725387e98d8db050f1c7a5fd0ec0a4596c587f92c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd74ac8e706e4c6aab17d2c594782487", "guid": "bfdfe7dc352907fc980b868725387e98251d465f7239177f9eb05186be427f1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c411fe588b757d227c61ad1c89d88a7", "guid": "bfdfe7dc352907fc980b868725387e98b9bf91278debb41b3833f220f1b435bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884e1a3be213cd55523d27be37425fb09", "guid": "bfdfe7dc352907fc980b868725387e98af769f021239e440439d2f37c2a8b47a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98115606d0ffc24f4ac9c88afff3c55dfa", "guid": "bfdfe7dc352907fc980b868725387e98f4410e2eefcc5e3073d1057d8a440acc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa5f5cdcc46d57636d9a0cc42c1f3f9e", "guid": "bfdfe7dc352907fc980b868725387e981c659a32af5c88dd60c9e7e92e503c73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e515a5f6cd74e005692b0ad4072b52d", "guid": "bfdfe7dc352907fc980b868725387e98385e9f712da19f3fdeab81bd33335d87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836cb5e5c0703af56d0680e34251a34f1", "guid": "bfdfe7dc352907fc980b868725387e98a44cf29d16204615eb6acc6a177ed90d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fe0844265eb0dc76e31bfa430cf0614", "guid": "bfdfe7dc352907fc980b868725387e981759ea69ef13d9431585b07246de10e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5b35abcc19413c74da9abc24fc89a2f", "guid": "bfdfe7dc352907fc980b868725387e98c7a014a0f2696eb65fdde5d95ff8a377"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5c8a67e88777b5098ff781b3dcb7932", "guid": "bfdfe7dc352907fc980b868725387e987d3c88b047f31c79453e480da1cee745"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7533ea0e9f7254068bb2f6d17b67a37", "guid": "bfdfe7dc352907fc980b868725387e98754dd466afb74dc55b83de8dbd18dd4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98890a1f0bd3de458565a342f8bd8afa67", "guid": "bfdfe7dc352907fc980b868725387e98d346057307b65a0c4cb63b7ce2cfbc65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cb9832de2b400a759886415b92e4038", "guid": "bfdfe7dc352907fc980b868725387e9831a5a7cf4456b84e8b2fb59d15681b76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98534cd942924ae3ccb6b5b61c4fcd374b", "guid": "bfdfe7dc352907fc980b868725387e98630b01bae14ac405faf80a562b3c32b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873dff826f516c69f6069fa97011bee80", "guid": "bfdfe7dc352907fc980b868725387e98c9f34912719e77ba96475461238e0e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bed96c72cbdeb3a40266490c8ae27678", "guid": "bfdfe7dc352907fc980b868725387e984867af3440057818079d08ea0c4d6195"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892fb486abff0bdedb1e744ea797c8e95", "guid": "bfdfe7dc352907fc980b868725387e9804dbc6bc4a995fc03805e33bbdc61583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986375d7979088ae270bbd20460a930fcf", "guid": "bfdfe7dc352907fc980b868725387e98d92cafd93ccc407f0b67993abcefdce5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef4562fd3eb478a295b786ec854ed774", "guid": "bfdfe7dc352907fc980b868725387e9820143a2be1e220fac8de61a11087c01f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b53338ef8304b5a4810d5d642fb652c", "guid": "bfdfe7dc352907fc980b868725387e98f5c29ea9255f8220e21e5404dc5a3f8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b95f853f5be06f615c8ad2e363b35740", "guid": "bfdfe7dc352907fc980b868725387e982693cc42ebb05a72e63217341d71bd9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b212963bbdb34fc195f6dd33bceb054", "guid": "bfdfe7dc352907fc980b868725387e985d0530ef6188ebe958e1f0d3f3298d82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862c45f60700ba1327e692090ca795849", "guid": "bfdfe7dc352907fc980b868725387e9829041b1e039eb006c470ff92591bf911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6b8506bb630ea24f5cbec731dad0f80", "guid": "bfdfe7dc352907fc980b868725387e98b92b9cf860855796967f283ce7415d09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a32f099446092fa79323f5df0ae4334f", "guid": "bfdfe7dc352907fc980b868725387e98debe9957367259f62596ede328df84af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985649ef8afcb550b9d57ad8a700d42a97", "guid": "bfdfe7dc352907fc980b868725387e98ec8f6bff25068400792a0da62301d62a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d97fd7deefaf6db6afe75dd61358de66", "guid": "bfdfe7dc352907fc980b868725387e981808cef69632857ddb48af8a1b5faf76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98116f47792018490865beb113e7f59d19", "guid": "bfdfe7dc352907fc980b868725387e980f230ea024b72dbc683a1fabead8d6bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b99d47b87bea264b9a6661493ad158b", "guid": "bfdfe7dc352907fc980b868725387e98deca3a41ff15842151e1be841cc3fff0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da68ff74aa860a28c7c1049271fd453e", "guid": "bfdfe7dc352907fc980b868725387e98f21ff164321f2d57d89d63e9fa8d5d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866ce6dfd52a85bb912d823b2b6b9cfaa", "guid": "bfdfe7dc352907fc980b868725387e981618ab1789cddc3783305bb624a1280e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98720b259569e2fb4d5b5f487e19796f28", "guid": "bfdfe7dc352907fc980b868725387e98c367e72e8bce33e83d8a76f05c126e9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7349c793fbdd102838754878f36dd1e", "guid": "bfdfe7dc352907fc980b868725387e98c36efdc476e07ebbd7263ace396ae758"}], "guid": "bfdfe7dc352907fc980b868725387e98053600d677a0ee3a6e8294a5f6c29926", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e98a7f5c11a3ddc6c6109c703c9daffc90f"}], "guid": "bfdfe7dc352907fc980b868725387e986ef21295949b1adf438aaa172cba7c15", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987799c6ff721a2f90b939c7d64edb78cc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e985c8c1a45791dbc15ae7565c9ac08e62e", "name": "AppCheckCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}