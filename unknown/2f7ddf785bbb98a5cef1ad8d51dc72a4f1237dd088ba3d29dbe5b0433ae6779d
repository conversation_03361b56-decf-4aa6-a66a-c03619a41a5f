{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988e00dc8ac96cb39bbe98f14d0a1fc199", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98411f2c88802d0d625eb1cb4edeb81f09", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e35561984780f8d70f07476e8e2865f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c20b9a2deeda570959f4fb600407456f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e35561984780f8d70f07476e8e2865f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832d4e367065d3f65b91a818cd9667205", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98084c653912314035b9555f83fb81d9b7", "guid": "bfdfe7dc352907fc980b868725387e98b619a11c0536449b7bdfca83d2ff423e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98dbdd4543e757afe338299b2c2ba794fe", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6517f1cead1a89f8390a9a596e0dea6", "guid": "bfdfe7dc352907fc980b868725387e987ad48f48f604eef08569edb112789b5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98044c8f3c91d0a3c04834e1ed7bcc1423", "guid": "bfdfe7dc352907fc980b868725387e9885a88e5ce90b4e913eb220a2f9c9d0df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889fd05b17044730350e8e2ef64ec1a0d", "guid": "bfdfe7dc352907fc980b868725387e9826a8713b81c88b3dc84d762c3a3e551f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dddaf3a5650e8fd5fa2c5b3ba3948a0", "guid": "bfdfe7dc352907fc980b868725387e982ecf36db95d2157a3c4f5af5c30a9da2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98119be80ec7cd114dcf197a3a60a5f48d", "guid": "bfdfe7dc352907fc980b868725387e9801ee9bd0d4d23b2fc06fc54a932294bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f09217e2dac2350f25fceff45568b14", "guid": "bfdfe7dc352907fc980b868725387e98fb8779d039b9d83a917a32899b718147"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866ab8f476ee152a0bae6bfccfbed430b", "guid": "bfdfe7dc352907fc980b868725387e9828371f253ce8d4618f213a387b84b7f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98865584e30aa2ed4e003499eeb1f0ca82", "guid": "bfdfe7dc352907fc980b868725387e98af4f7aefc389ce4865c5f258ee734884"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e1abec116a4b2d0b328bb4099f47a17", "guid": "bfdfe7dc352907fc980b868725387e989826a372897383a897d574e07ad5568c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d5fd299ed4e1dc8864b22587f04ed03", "guid": "bfdfe7dc352907fc980b868725387e982e346fbde3ba1c74a791469ab12b4d7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ff568c314f45391191453ad5f4a2e79", "guid": "bfdfe7dc352907fc980b868725387e98848d587a0bd5f62f07c7f71d397e594b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b10167166e9691cbaf0230181b78e79", "guid": "bfdfe7dc352907fc980b868725387e989ac0ad8cab767967267e1aca16a170ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e57a796b93fac34057a381519410618f", "guid": "bfdfe7dc352907fc980b868725387e986ce7721e0307eec90fb6ab7ab3c2f268"}], "guid": "bfdfe7dc352907fc980b868725387e98f5aaf47589ea3de63f69de06034f5b88", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e980350257ef1326b3c8aa464b3d8f49fbb"}], "guid": "bfdfe7dc352907fc980b868725387e984f391c51027c55684ad684f27233a506", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9807a2e0c570aa52205c9e0daf9082bd16", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e982ddcfa578de5af18d3815105c005e148", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}