{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983d09faba59d9a109d8ff53b97d8127bc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985fd457d4126c5f84fe2f7ef4e9c4e8f8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f25ad07a491786d542111bffa7e22a5a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987204844d4668d78c9ef1854935a22c53", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f25ad07a491786d542111bffa7e22a5a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98adfe3990b4d3c21898f8b81aea3d2736", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b74cac4045d10e6349660a6578e841a7", "guid": "bfdfe7dc352907fc980b868725387e982c54677999d4caaf2d7ca3ca5325f2f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850426e414487754370571d5bbdf30cf6", "guid": "bfdfe7dc352907fc980b868725387e98eda962ffe074c071d43fc43eb6aead97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b6c4a0a1220c4170dbeddaa9cf317d2", "guid": "bfdfe7dc352907fc980b868725387e98035fa8f32a17920c8c6b8a0b55414c1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d90e1ecfb86bac9334fe728ec974f7f6", "guid": "bfdfe7dc352907fc980b868725387e980dc62edfe26a5de793fa5e7fa6a50e97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a3ffc71edadc7236fedf5d797166427", "guid": "bfdfe7dc352907fc980b868725387e987f86521fe1331153333d827a08553888", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0422c4da957861f6377881aac4c57e4", "guid": "bfdfe7dc352907fc980b868725387e9848cac8da30bb1e100d35ee3736b0052e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98437f3e2156a2d0bbaa1ba66fb55a7dff", "guid": "bfdfe7dc352907fc980b868725387e98f4ec99f00622b79f72d7a579c8884799", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2b1a3d35d24e71f52beaf7bf8ecff57", "guid": "bfdfe7dc352907fc980b868725387e9850253ff828a34d8a899de9ef97c78730", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6f9404e3593c3b6ce0b3fcb74599402", "guid": "bfdfe7dc352907fc980b868725387e9860ded3e0ebe69e3305ea189008573d71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815b6b460d2c270874618558e28b98e73", "guid": "bfdfe7dc352907fc980b868725387e98fe56bfb4f86822db1f77551991e06bda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98823dad19de1fee0028c0b1c32eb0a1ab", "guid": "bfdfe7dc352907fc980b868725387e9876e141f9fa6417a1f0bf5fc5b145d1e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2c774e2d1bda9619d38bcbe90589fff", "guid": "bfdfe7dc352907fc980b868725387e987eb4c3ba577faafd8ea4ee0677f8311e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b9cab742e84dd30904673b507af325e", "guid": "bfdfe7dc352907fc980b868725387e986f889a884066fe9f17f3962c41e06e9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e4a6ff6cc30fb1fbcd48e1189cd08ae", "guid": "bfdfe7dc352907fc980b868725387e98353287d629f5ca9985e03d265fefdb86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cfbf3e34beebd6b4b36d408d983b09b", "guid": "bfdfe7dc352907fc980b868725387e982c689ee070ca55f4ee55c4fc36a6d8f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844460089359614214abc2ab162e2a9e6", "guid": "bfdfe7dc352907fc980b868725387e98c263ba50b7a5917835cd9ab109778ad3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899598ee8a275738efb92f1be91588230", "guid": "bfdfe7dc352907fc980b868725387e9878f96e624bb802456bd6016250cb86fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828dc28f90f3900ca60d73664e103a102", "guid": "bfdfe7dc352907fc980b868725387e98b8c3c3e7a8f2c06cb28af5132c092f54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e0e7c46a67efd7a9afaed2574e80c1c", "guid": "bfdfe7dc352907fc980b868725387e98ff086931041696a604561d860c1c5527", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac93c810b10d1d343dff1db38c285ee3", "guid": "bfdfe7dc352907fc980b868725387e98b7da19cb85acbf98f22b3417d93727b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b17597d0f205467e1361d7f9d2afb1a", "guid": "bfdfe7dc352907fc980b868725387e9869a754a92c56e1e8aa9b25b7bdf89fba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cdf2af632fe914effa628a4eec1a6ff", "guid": "bfdfe7dc352907fc980b868725387e980f3a0895178e599ffe687f4c406e9f66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c78e58bc22776e43718f3da250a246ee", "guid": "bfdfe7dc352907fc980b868725387e985f1d12f47e5acd4655043fc6b9af7f7d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814b7518c7ec6c47228f02941713430a8", "guid": "bfdfe7dc352907fc980b868725387e987c1e07d72c184f07d03134c9e8465f05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5a5fcd4a7b1c8f86352b42e1665a440", "guid": "bfdfe7dc352907fc980b868725387e98ac9fccbd37246dd6dd30acadef790131", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98500c323914c578c5439bd08453108d60", "guid": "bfdfe7dc352907fc980b868725387e980290ffd84516d935061dc7747f4cdc70"}], "guid": "bfdfe7dc352907fc980b868725387e98c81d7cbb037b433b283d549d05e8f5ab", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a8762adecad2e905b4a42fd39ecfdec2", "guid": "bfdfe7dc352907fc980b868725387e9864b8b11a67ff2c24da16676f6441ad3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98500aea28a190a10dca17cc8b917b68eb", "guid": "bfdfe7dc352907fc980b868725387e9835e66c05b7f095e12a9d8f16a1861c56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875f1eaf881d4fb90979ccdaa0007da9b", "guid": "bfdfe7dc352907fc980b868725387e985401c44678571efb670f349ed418d31b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8353a83e9300479b8c7ff9ed06b13ef", "guid": "bfdfe7dc352907fc980b868725387e9823f8770a627d39b6c41917a681b3f71d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8346f988b2d573177bbb05228ff2c51", "guid": "bfdfe7dc352907fc980b868725387e98dd9eea2661de73c2d4d870a9fd9d92bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981394de974671e9eed5a19b1bf38ecff1", "guid": "bfdfe7dc352907fc980b868725387e98d3d9b8f4ca604479e2ee24f132d05329"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fb32d03c7d3e987f9f81d104b0925d1", "guid": "bfdfe7dc352907fc980b868725387e980fa9ef99d9b8116dac256aa86cf7d14d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f580511b1a2932360ca62151c1b916e", "guid": "bfdfe7dc352907fc980b868725387e98e0518c51f157c8b9227842787b3660a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989daab40c5403a4081d104bad84c22c81", "guid": "bfdfe7dc352907fc980b868725387e989d330dbddc0eccd3a7548e9dc719d70b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864ecb0c1c67fc013f98e0c054adebf96", "guid": "bfdfe7dc352907fc980b868725387e983c6683568797cd557c661a4df934801a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98852ae9d1cbfb5792ae65c0dcd065c179", "guid": "bfdfe7dc352907fc980b868725387e9861e94636e4b4e7f73c55d78169fabb23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981782aa65fb51d10f117f6e5b8367f422", "guid": "bfdfe7dc352907fc980b868725387e9868363981113a9c3aedb7c7c16a8ae96b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98286b5899e560b7288743de89c45b9afb", "guid": "bfdfe7dc352907fc980b868725387e984665174216f8c252b72513c02f276c45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfe9eea5885b53ca1ab8374ef65f2dcf", "guid": "bfdfe7dc352907fc980b868725387e981f4f5e264c33dafebb00d780fd86238c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868a111a49def8bb2a695fe1bf26d2d7e", "guid": "bfdfe7dc352907fc980b868725387e984ddc37ca168a2fc53f8d341653198ca6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fc7d117ba549ae1ee6d46608e2c6b33", "guid": "bfdfe7dc352907fc980b868725387e986a2aa9444fc288921ccde2bf36c26386"}], "guid": "bfdfe7dc352907fc980b868725387e98154cf320a9270e2d066d2bc0d30bbf09", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98241a1c27e578a9a9a496bff7f570b114", "guid": "bfdfe7dc352907fc980b868725387e98ed90a2496bd690b0fa0aa09d7cb67d64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d05a97faeb775352b784fcd3a04da5fd", "guid": "bfdfe7dc352907fc980b868725387e98b69dcd479d16436e54dcd99fb2dd3e94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f646bf1bf771be2d8c368d39254dddcf", "guid": "bfdfe7dc352907fc980b868725387e983e8054d86a1526941430cf15e660b349"}], "guid": "bfdfe7dc352907fc980b868725387e98f9c42643bb4e72cc41a5e0332c2d54d9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987b8d73dd5528dc6e28bc96a478b90b66", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e982b34d89dfe547c0de86b9b9f6c95da19", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}