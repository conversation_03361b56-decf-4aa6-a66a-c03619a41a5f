import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/WalletTransactionModel.dart';
import 'package:emartdriver/services/TaEntregueApiService.dart';
import 'package:emartdriver/ui/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class WalletBalanceScreen extends StatefulWidget {
  const WalletBalanceScreen({Key? key}) : super(key: key);

  @override
  State<WalletBalanceScreen> createState() => _WalletBalanceScreenState();
}

class _WalletBalanceScreenState extends State<WalletBalanceScreen> {
  bool _isLoading = true;
  double _walletBalance = 0.0;
  List<WalletTransactionModel> _transactions = [];
  String _errorMessage = '';

  bool _isRequestingWithdrawal = false;

  @override
  void initState() {
    super.initState();
    _loadWalletData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadWalletData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final idRemoto = MyAppState.currentUser!.userID;

      final balanceResult = await TaEntregueApiService.getWalletBalance(
        idRemoto: idRemoto,
      );

      bool success = balanceResult['success'] == true;
      final data = balanceResult['data'];
      String errorMessage = '';
      double walletBalance = _walletBalance;

      if (success && data != null) {
        final saldoStr = data['saldo']?.toString() ?? '0';
        final saldoNormalizado = saldoStr.replaceAll(',', '.');
        walletBalance = double.tryParse(saldoNormalizado) ?? 0.0;

        final transactionsList =
            await TaEntregueApiService.getTransactionHistory(
          idRemoto: idRemoto,
        );

        setState(() {
          _walletBalance = walletBalance;
          _transactions = transactionsList;
          _isLoading = false;
        });
      } else if (success && data == null) {
        errorMessage = 'Dados da carteira não encontrados';
        setState(() {
          _errorMessage = errorMessage;
          _isLoading = false;
        });
      } else if (!success) {
        errorMessage =
            balanceResult['message'] ?? 'Erro ao carregar saldo da carteira';
        setState(() {
          _errorMessage = errorMessage;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Erro ao carregar dados da carteira: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: Column(
        children: [
          if (_errorMessage.isNotEmpty)
            Container(
              width: double.infinity,
              color: Colors.amber[200],
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              child: Row(
                children: [
                  const Icon(Icons.warning_amber_rounded,
                      color: Colors.black87),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _errorMessage,
                      style: const TextStyle(
                        color: Colors.black87,
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          Expanded(
            child: _isLoading
                ? const LoadingWidget()
                : _errorMessage.isNotEmpty
                    ? _buildErrorWidget()
                    : _buildWalletContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 60,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadWalletData,
            child: const Text('Tentar novamente'),
          ),
        ],
      ),
    );
  }

  Widget _buildWalletContent() {
    return RefreshIndicator(
      onRefresh: _loadWalletData,
      child: Column(
        children: [
          const SizedBox(height: 10),
          _buildBalanceCard(),
          const SizedBox(height: 16),
          _buildTransactionsList(),
        ],
      ),
    );
  }

  Future<void> _requestWithdrawal() async {
    if (_walletBalance <= 0) {
      _showErrorSnackBar('Não há saldo disponível para saque');
      return;
    }

    setState(() {
      _isRequestingWithdrawal = true;
    });

    try {
      final idRemoto = MyAppState.currentUser!.userID;

      final result = await TaEntregueApiService.requestWithdrawal(
        idRemoto: idRemoto,
        valor: _walletBalance,
      );

      if (result['status'] == 'success') {
        _showSuccessSnackBar(
            result['message'] ?? 'Solicitação de saque realizada com sucesso');

        await _loadWalletData();
      } else {
        _showErrorSnackBar(result['message'] ?? 'Erro ao solicitar saque');
      }
    } catch (e) {
      _showErrorSnackBar('Erro ao solicitar saque: $e');
    } finally {
      setState(() {
        _isRequestingWithdrawal = false;
      });
    }
  }

  void _showWithdrawalDialog() {
    final currencyFormat =
        NumberFormat.currency(locale: 'pt_BR', symbol: 'R\$');

    if (_walletBalance <= 0) {
      _showErrorSnackBar('Não há saldo disponível para saque');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmar Saque'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Você está solicitando o saque do valor total disponível:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Text(
              currencyFormat.format(_walletBalance),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xff425799),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Deseja confirmar esta solicitação?',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: _isRequestingWithdrawal
                ? null
                : () {
                    Navigator.pop(context);
                    _requestWithdrawal();
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xff425799),
            ),
            child: _isRequestingWithdrawal
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Confirmar',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                    )),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  Widget _buildBalanceCard() {
    final currencyFormat =
        NumberFormat.currency(locale: 'pt_BR', symbol: 'R\$');

    return Column(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [
                Color(0xff425799),
                Color.fromARGB(255, 136, 158, 233),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(26),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              const Icon(Icons.account_balance_wallet,
                  color: Colors.white, size: 40),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Saldo disponível',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      currencyFormat.format(_walletBalance),
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: _showWithdrawalDialog,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            backgroundColor: const Color(0xff425799),
          ),
          child: const Text(
            'Solicitar Saque Total',
            style: TextStyle(fontSize: 16, color: Colors.white),
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionsList() {
    if (_transactions.isEmpty) {
      return const Expanded(
        child: Center(
          child: Text(
            'Nenhuma transação encontrada',
            style: TextStyle(fontSize: 16),
          ),
        ),
      );
    }

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              'Histórico de transações',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _transactions.length,
              itemBuilder: (context, index) {
                return _buildTransactionItem(_transactions[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(WalletTransactionModel transaction) {
    final currencyFormat =
        NumberFormat.currency(locale: 'pt_BR', symbol: 'R\$');
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    final date = DateTime.tryParse(transaction.dataTransacao) ?? DateTime.now();

    final isCredit = transaction.isCredit();
    final valueColor = isCredit ? Colors.green : Colors.red;
    final valuePrefix = isCredit ? '+' : '-';

    return Card(
      margin: const EdgeInsets.only(bottom: 10),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: valueColor.withAlpha(26),
          child: Icon(
            isCredit ? Icons.arrow_downward : Icons.arrow_upward,
            color: valueColor,
          ),
        ),
        title: Text(
          transaction.label,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(
          dateFormat.format(date),
          style: TextStyle(color: Colors.grey[600]),
        ),
        trailing: Text(
          '$valuePrefix ${currencyFormat.format(transaction.valor)}',
          style: TextStyle(
            color: valueColor,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ),
    );
  }
}
