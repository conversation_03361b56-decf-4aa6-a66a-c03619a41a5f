import 'dart:developer';
import 'dart:ui' as ui;

import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MapService {
  static final MapService _instance = MapService._internal();
  factory MapService() => _instance;
  MapService._internal();

  BitmapDescriptor? _deliveryPersonIcon;

  BitmapDescriptor? get deliveryPersonIcon => _deliveryPersonIcon;

  Future<void> loadCustomMarkerIcon() async {
    try {
      final Uint8List markerIcon =
          await getBytesFromAsset('assets/images/motoentregador.png', 40);
      _deliveryPersonIcon = BitmapDescriptor.bytes(markerIcon);

      _deliveryPersonIcon ??=
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    } catch (e) {
      log("Error in loadCustomMarkerIcon: $e");

      _deliveryPersonIcon =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    }
  }

  Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  void clearMarkersExceptCurrentPosition(Map<String, Marker> markers) {
    markers.removeWhere((key, value) => key != 'current_position');
  }

  void forceMapReload(GoogleMapController mapController) {
    // Implementação para forçar reload do mapa se necessário
  }

  void updateCurrentPositionMarker(Map<String, Marker> markers,
      LatLng currentPosition, BitmapDescriptor? icon,
      {double heading = 0.0}) {
    markers['current_position'] = Marker(
      markerId: const MarkerId('current_position'),
      position: currentPosition,
      icon: icon ?? BitmapDescriptor.defaultMarker,
      infoWindow: const InfoWindow(title: 'Sua localização'),
      zIndex: 2,
      rotation: heading,
      anchor: const Offset(0.5, 0.5),
    );
  }

  /// Zoom in on the map
  Future<void> zoomIn(GoogleMapController controller) async {
    try {
      final currentZoom = await controller.getZoomLevel();
      double newZoom = currentZoom + 1.0;
      newZoom = newZoom > 20.0 ? 20.0 : newZoom;

      await controller.animateCamera(CameraUpdate.zoomTo(newZoom));
    } catch (e) {
      log("Erro ao aumentar zoom: $e");
    }
  }

  /// Zoom out on the map
  Future<void> zoomOut(GoogleMapController controller) async {
    try {
      final currentZoom = await controller.getZoomLevel();
      double newZoom = currentZoom - 1.0;
      newZoom = newZoom < 2.0 ? 2.0 : newZoom;

      await controller.animateCamera(CameraUpdate.zoomTo(newZoom));
    } catch (e) {
      log("Erro ao reduzir zoom: $e");
    }
  }

  /// Center map on current location
  Future<void> centerOnLocation(
    GoogleMapController controller,
    LatLng position, {
    double zoom = 17.0,
  }) async {
    try {
      await controller.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: position,
            zoom: zoom,
            tilt: 0,
          ),
        ),
      );
    } catch (e) {
      log("Erro ao centralizar mapa: $e");
    }
  }
}
