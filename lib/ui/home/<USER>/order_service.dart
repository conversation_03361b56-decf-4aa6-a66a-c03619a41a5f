import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

class OrderService {
  static final OrderService _instance = OrderService._internal();
  factory OrderService() => _instance;
  OrderService._internal();

  final String _currentUserId = FirebaseAuth.instance.currentUser?.uid ?? '';

  /// Check for accepted orders (including return orders)
  Future<String?> checkForAcceptedOrder() async {
    try {
      // Check for return orders first
      final returnQuery = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: _currentUserId)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1);

      final returnSnapshot = await returnQuery.get();

      log("Verificando pedidos de devolução: ${returnSnapshot.docs.length} encontrados");

      if (returnSnapshot.docs.isNotEmpty) {
        final orderId = returnSnapshot.docs.first.id;
        log("Pedido de devolução encontrado com ID: $orderId");
        return orderId;
      }

      // Check for regular accepted orders
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: _currentUserId)
          .where('status', whereIn: [
        OrderStatus.driverAccepted.description,
        OrderStatus.driverOnTheWay.description,
        OrderStatus.driverPending.description
      ]).limit(1);

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        return snapshot.docs.first.id;
      }

      return null;
    } catch (e) {
      log("Error checking for accepted orders: $e");
      return null;
    }
  }

  /// Accept a new order
  Future<OrderModel?> acceptOrder(String orderId) async {
    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);

      await docRef.update({
        "entregador_id": _currentUserId,
        "horaAceite": Timestamp.now(),
        "status": OrderStatus.driverAccepted.description,
      });

      final updatedDoc = await docRef.get();
      if (updatedDoc.exists) {
        return OrderModel.fromJson(updatedDoc.data()!);
      }
      return null;
    } catch (e) {
      log("Erro ao aceitar pedido: $e");
      rethrow;
    }
  }

  /// Listen to a specific order by ID
  StreamSubscription<DocumentSnapshot> listenToOrder(
    String orderId, {
    required Function(OrderModel) onOrderUpdate,
    required VoidCallback onOrderNotFound,
  }) {
    final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);

    return docRef.snapshots().listen((docSnapshot) {
      if (docSnapshot.exists) {
        final order =
            OrderModel.fromJson(docSnapshot.data() as Map<String, dynamic>);
        onOrderUpdate(order);
      } else {
        onOrderNotFound();
      }
    }, onError: (e) {
      log("Error listening to order $orderId: $e");
    });
  }

  /// Get initial order data
  Future<OrderModel?> getOrder(String orderId) async {
    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        return OrderModel.fromJson(docSnapshot.data() as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      log("Error fetching order $orderId: $e");
      return null;
    }
  }

  /// Listen to available orders
  StreamSubscription<QuerySnapshot> listenToAvailableOrders({
    required Function(QuerySnapshot) onOrdersUpdate,
  }) {
    final query = FirebaseFirestore.instance
        .collection(ORDERS)
        .where('status', isEqualTo: OrderStatus.driverSearching.description);

    return query.snapshots().listen((querySnapshot) {
      onOrdersUpdate(querySnapshot);
    }, onError: (e) {
      log("Error listening to available orders: $e");
    });
  }

  /// Get available orders snapshot
  Future<QuerySnapshot> getAvailableOrders() async {
    try {
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('status', isEqualTo: OrderStatus.driverSearching.description);

      return await query.get();
    } catch (e) {
      log("Error fetching available orders: $e");
      rethrow;
    }
  }

  /// Get return orders for current user
  Future<QuerySnapshot> getReturnOrders() async {
    try {
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: _currentUserId)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1);

      return await query.get();
    } catch (e) {
      log("Error fetching return orders: $e");
      rethrow;
    }
  }

  /// Confirm return order completion
  Future<void> confirmReturnOrder(String orderId) async {
    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);

      await docRef.update({
        'status': OrderStatus.completed.description,
        'has_return': false,
      });

      log("Return order $orderId confirmed successfully");
    } catch (e) {
      log("Error confirming return order $orderId: $e");
      rethrow;
    }
  }

  /// Check if order is already accepted by another driver
  bool isOrderAcceptedByOther(OrderModel order) {
    return order.status?.description ==
            OrderStatus.driverAccepted.description &&
        order.entregador_id != null &&
        order.entregador_id!.isNotEmpty &&
        order.entregador_id != _currentUserId;
  }

  /// Group orders by store
  Map<String, List<OrderModel>> groupOrdersByStore(
    List<OrderModel> orders, {
    required double maxDistanceKm,
    required double currentLat,
    required double currentLng,
    required Function(double, double, double, double) calculateDistance,
    required Function(GeoPoint?) getLatLng,
  }) {
    Map<String, List<OrderModel>> ordersByStore = {};

    for (OrderModel order in orders) {
      final storePos = getLatLng(order.vendor.address_store?.location.geoPoint);

      if (storePos != null) {
        double distanceInKm = calculateDistance(
          currentLat,
          currentLng,
          storePos.latitude,
          storePos.longitude,
        );

        if (distanceInKm <= maxDistanceKm) {
          String storeId =
              order.vendor.id.isNotEmpty == true ? order.vendor.id : order.id;

          if (!ordersByStore.containsKey(storeId)) {
            ordersByStore[storeId] = [];
          }
          ordersByStore[storeId]!.add(order);
        } else {
          log("Store for order ${order.id} filtered out: distance ${distanceInKm.toStringAsFixed(1)} km exceeds limit of $maxDistanceKm km");
        }
      }
    }

    return ordersByStore;
  }

  /// Convert QuerySnapshot to OrderModel list
  List<OrderModel> querySnapshotToOrderList(QuerySnapshot querySnapshot) {
    return querySnapshot.docs.map((doc) {
      final data = doc.data() as Map<String, dynamic>;
      return OrderModel.fromJson(data);
    }).toList();
  }
}
