import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  /// Show order bottom sheet for new orders
  void showNewOrderNotification({
    required BuildContext context,
    required OrderModel order,
    required double distanceInKm,
    required double durationInMinutes,
    required VoidCallback onAccept,
    required VoidCallback onDecline,
    required bool pedidoAceito,
  }) {
    try {
      showOrderBottomSheet(
        context: context,
        order: order,
        distanceInKm: distanceInKm,
        durationInMinutes: durationInMinutes,
        onAccept: onAccept,
        onDecline: onDecline,
        pedidoAceito: pedidoAceito,
      );
    } catch (e) {
      log("Error showing new order notification: $e");
    }
  }

  /// Show order accepted dialog
  void showOrderAcceptedNotification({
    required BuildContext context,
    required OrderModel order,
  }) {
    try {
      presentOrderAcceptedDialog(context, order);
    } catch (e) {
      log("Error showing order accepted notification: $e");
    }
  }

  /// Show store info dialog
  void showStoreInfoNotification({
    required BuildContext context,
    required OrderModel order,
  }) {
    try {
      showStoreInfoDialog(context, order);
    } catch (e) {
      log("Error showing store info notification: $e");
    }
  }

  /// Show success snackbar
  void showSuccessMessage({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 3),
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: duration,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show error snackbar
  void showErrorMessage({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 4),
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Show info snackbar
  void showInfoMessage({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 3),
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.blue,
        duration: duration,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show warning snackbar
  void showWarningMessage({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 3),
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange,
        duration: duration,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show confirmation dialog
  Future<bool?> showConfirmationDialog({
    required BuildContext context,
    required String title,
    required String message,
    String confirmText = 'Confirmar',
    String cancelText = 'Cancelar',
    Color? confirmColor,
  }) async {
    if (!context.mounted) return null;

    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(cancelText),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: confirmColor ?? Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: Text(confirmText),
            ),
          ],
        );
      },
    );
  }

  /// Show loading dialog
  void showLoadingDialog({
    required BuildContext context,
    String message = 'Carregando...',
  }) {
    if (!context.mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 20),
              Expanded(child: Text(message)),
            ],
          ),
        );
      },
    );
  }

  /// Hide loading dialog
  void hideLoadingDialog(BuildContext context) {
    if (context.mounted) {
      Navigator.of(context).pop();
    }
  }

  /// Show order status update notification
  void showOrderStatusUpdate({
    required BuildContext context,
    required String status,
    required String orderNumber,
  }) {
    String message;
    Color backgroundColor;

    switch (status.toLowerCase()) {
      case 'accepted':
        message = 'Pedido #$orderNumber aceito com sucesso!';
        backgroundColor = Colors.green;
        break;
      case 'picked_up':
        message = 'Pedido #$orderNumber coletado!';
        backgroundColor = Colors.blue;
        break;
      case 'delivered':
        message = 'Pedido #$orderNumber entregue com sucesso!';
        backgroundColor = Colors.green;
        break;
      case 'cancelled':
        message = 'Pedido #$orderNumber foi cancelado.';
        backgroundColor = Colors.red;
        break;
      default:
        message = 'Status do pedido #$orderNumber atualizado.';
        backgroundColor = Colors.blue;
    }

    showCustomMessage(
      context: context,
      message: message,
      backgroundColor: backgroundColor,
    );
  }

  /// Show custom message with custom styling
  void showCustomMessage({
    required BuildContext context,
    required String message,
    Color backgroundColor = Colors.blue,
    Color textColor = Colors.white,
    Duration duration = const Duration(seconds: 3),
    IconData? icon,
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            if (icon != null) ...[
              Icon(icon, color: textColor),
              const SizedBox(width: 8),
            ],
            Expanded(
              child: Text(
                message,
                style: TextStyle(color: textColor),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: duration,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show delayed notifications (for order acceptance flow)
  void showDelayedNotifications({
    required BuildContext context,
    required OrderModel order,
  }) {
    // Show order accepted dialog after 1 second
    Future.delayed(const Duration(seconds: 1), () {
      if (context.mounted) {
        showOrderAcceptedNotification(context: context, order: order);

        // Show store info dialog after additional 500ms
        Future.delayed(const Duration(milliseconds: 500), () {
          if (context.mounted) {
            showStoreInfoNotification(context: context, order: order);
          }
        });
      }
    });
  }
}
