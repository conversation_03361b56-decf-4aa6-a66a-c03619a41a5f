import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:emartdriver/model/OrderModel.dart';

class MultipleOrdersDialog extends StatelessWidget {
  final List<OrderModel> orders;
  final LatLng? currentPosition;
  final Function(OrderModel) onOrderSelected;
  final Function(double, double, double, double) calculateDistance;
  final Function(GeoPoint?) getLatLng;

  const MultipleOrdersDialog({
    super.key,
    required this.orders,
    required this.currentPosition,
    required this.onOrderSelected,
    required this.calculateDistance,
    required this.getLatLng,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.70,
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 20),
              GestureDetector(
                onTap: () {},
                child: Container(
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              )
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Loja: ${orders.first.vendor.title}',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xff425799),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Container(
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              color: Color.fromARGB(39, 201, 212, 247),
            ),
            width: MediaQuery.of(context).size.width,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset('assets/images/caixa.png', height: 30, width: 30),
                const SizedBox(width: 8),
                Text(
                  "${orders.length} pedidos disponíveis",
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          const SizedBox(height: 15),
          Expanded(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: orders.length,
              itemBuilder: (context, index) {
                final order = orders[index];
                LatLng? storePos =
                    getLatLng(order.vendor.address_store?.location.geoPoint)
                        as LatLng?;
                double storeDistance = 0;

                if (currentPosition != null && storePos != null) {
                  storeDistance = calculateDistance(
                    currentPosition!.latitude,
                    currentPosition!.longitude,
                    storePos.latitude,
                    storePos.longitude,
                  );
                }

                return Card(
                  margin:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  elevation: 3,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: const BorderSide(color: Color(0xff425799), width: 1),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.all(16),
                    title: Text(
                      "Pedido #${order.id.substring(0, 8)}",
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Color(0xff425799),
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 8),
                        Text(
                          "Cliente: ${order.author.firstName} ${order.author.lastName}",
                          style: const TextStyle(fontSize: 14),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          "Valor: R\$ ${order.price ?? '0,00'}",
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.green,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          "Distância da loja: ${storeDistance.toStringAsFixed(1)} km",
                          style:
                              const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ),
                    trailing: const Icon(
                      Icons.arrow_forward_ios,
                      color: Color(0xff425799),
                      size: 16,
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      onOrderSelected(order);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
