import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class OrderMarkerManager {
  static BitmapDescriptor? customMarkerIcon;
  static BitmapDescriptor? storeMarkerIcon;
  static BitmapDescriptor? clientMarkerIcon;

  static Future<void> loadCustomMarkerIcons() async {
    try {
      customMarkerIcon = await BitmapDescriptor.fromAssetImage(
        const ImageConfiguration(size: Size(48, 48)),
        'assets/images/delivery_location.png',
      );
      storeMarkerIcon = await BitmapDescriptor.fromAssetImage(
        const ImageConfiguration(size: Size(48, 48)),
        'assets/images/store_location.png',
      );
      clientMarkerIcon = await BitmapDescriptor.fromAssetImage(
        const ImageConfiguration(size: Size(48, 48)),
        'assets/images/client_location.png',
      );
    } catch (e) {
      log('Error loading custom marker icons: $e');
    }
  }

  static LatLng? getLatLng(GeoPoint? geoPoint) {
    if (geoPoint != null) {
      return LatLng(geoPoint.latitude, geoPoint.longitude);
    }
    return null;
  }

  static Set<Marker> createOrderMarkers({
    required List<OrderModel> orders,
    required LatLng? currentPosition,
    required Function(List<OrderModel>) onMarkerTap,
  }) {
    Set<Marker> markers = {};

    // Add current position marker
    if (currentPosition != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('current_position'),
          position: currentPosition,
          icon: customMarkerIcon ?? BitmapDescriptor.defaultMarker,
          infoWindow: const InfoWindow(title: 'Sua Localização'),
        ),
      );
    }

    // Group orders by store location
    Map<String, List<OrderModel>> ordersByStore = {};
    for (OrderModel order in orders) {
      LatLng? storePos = getLatLng(order.vendor.address_store?.location.geoPoint);
      if (storePos != null) {
        String key = '${storePos.latitude}_${storePos.longitude}';
        ordersByStore[key] ??= [];
        ordersByStore[key]!.add(order);
      }
    }

    // Create markers for each store location
    ordersByStore.forEach((key, storeOrders) {
      if (storeOrders.isNotEmpty) {
        LatLng? storePos = getLatLng(storeOrders.first.vendor.address_store?.location.geoPoint);
        if (storePos != null) {
          markers.add(
            Marker(
              markerId: MarkerId('store_$key'),
              position: storePos,
              icon: storeMarkerIcon ?? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
              infoWindow: InfoWindow(
                title: storeOrders.first.vendor.title,
                snippet: '${storeOrders.length} pedido(s) disponível(is)',
              ),
              onTap: () => onMarkerTap(storeOrders),
            ),
          );
        }
      }
    });

    return markers;
  }

  static Set<Marker> createAcceptedOrderMarkers({
    required OrderModel order,
    required LatLng? currentPosition,
    required LatLng? storePosition,
    required LatLng? clientPosition,
  }) {
    Set<Marker> markers = {};

    // Add current position marker
    if (currentPosition != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('current_position'),
          position: currentPosition,
          icon: customMarkerIcon ?? BitmapDescriptor.defaultMarker,
          infoWindow: const InfoWindow(title: 'Sua Localização'),
        ),
      );
    }

    // Add store marker
    if (storePosition != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('store_position'),
          position: storePosition,
          icon: storeMarkerIcon ?? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: InfoWindow(
            title: 'Loja: ${order.vendor.title}',
            snippet: 'Local de coleta',
          ),
        ),
      );
    }

    // Add client marker
    if (clientPosition != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('client_position'),
          position: clientPosition,
          icon: clientMarkerIcon ?? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
          infoWindow: InfoWindow(
            title: 'Cliente: ${order.author.firstName} ${order.author.lastName}',
            snippet: 'Local de entrega',
          ),
        ),
      );
    }

    return markers;
  }

  static Set<Marker> createReturnOrderMarkers({
    required OrderModel order,
    required LatLng? currentPosition,
    required LatLng? storePosition,
  }) {
    Set<Marker> markers = {};

    // Add current position marker
    if (currentPosition != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('current_position'),
          position: currentPosition,
          icon: customMarkerIcon ?? BitmapDescriptor.defaultMarker,
          infoWindow: const InfoWindow(title: 'Sua Localização'),
        ),
      );
    }

    // Add store marker for return
    if (storePosition != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('return_store_position'),
          position: storePosition,
          icon: storeMarkerIcon ?? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange),
          infoWindow: InfoWindow(
            title: 'Devolução: ${order.vendor.title}',
            snippet: 'Local de devolução',
          ),
        ),
      );
    }

    return markers;
  }
}
