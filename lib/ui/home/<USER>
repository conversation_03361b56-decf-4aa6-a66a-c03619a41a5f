import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:flutter/material.dart';

void presentOrderAcceptedDialog(BuildContext context, OrderModel order,
    {String? extraMessage}) async {
  // Primeiro, mostrar o diálogo de devolução se necessário
  if (order.has_return) {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Column(
            children: [
              Image.asset(
                'assets/images/motoca.png',
                height: 64,
                width: 64,
              ),
              const SizedBox(height: 16),
              const Text(
                'Devolução de pedidos',
                style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Color(0xff425799)),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                '''Caso o pedido não seja entregue,
faça sua devolução em loja.
                ''',
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              const SizedBox(height: 16),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromARGB(255, 255, 85, 0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                onPressed: () async {
                  Navigator.pop(context);
                },
                child: const Text(
                  'OK, entendi.',
                  style: TextStyle(fontSize: 16, color: Colors.white),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Diálogo principal
  if (!order.has_return) {
    final result =
        await coletarPedido(context, order, extraMessage: extraMessage);
    return;
  }
}

Future<bool?> coletarPedido(BuildContext context, OrderModel order,
    {String? extraMessage}) async {
  final result = await showDialog<bool?>(
    barrierDismissible: false,
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Column(
          children: [
            Image.asset(
              'assets/images/entregador_icon.png',
              height: 64,
              width: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Coleta na Loja',
              style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Color(0xff425799)),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              children: [
                const Text(
                  '''Confirme se você realmente
chegou à loja para coleta.
                  ''',
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                if (extraMessage != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      extraMessage,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            const SizedBox(height: 16),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color.fromARGB(255, 19, 188, 0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              onPressed: () async {
                // Atualizar o status do pedido para "a caminho"
                try {
                  final docRef = FirebaseFirestore.instance
                      .collection(ORDERS)
                      .doc(order.id);

                  await docRef.update({
                    'status': OrderStatus.driverOnTheWay.description,
                  });

                  // Navegar para a tela de endereço do cliente
                  if (context.mounted) {
                    Navigator.pop(context, true);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => EnderecoCliente(order: order),
                      ),
                    );
                  }
                } catch (e) {
                  print("Erro ao atualizar status do pedido: $e");
                  if (context.mounted) {
                    Navigator.pop(context, false);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text("Erro ao confirmar coleta: $e")),
                    );
                  }
                }
              },
              child: const Text(
                'Confirmar',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
            const SizedBox(height: 16),
            GestureDetector(
              onTap: () {
                Navigator.pop(context, false);
              },
              child: const Text('voltar',
                  style: TextStyle(fontSize: 17, color: Colors.orange)),
            ),
          ],
        ),
      );
    },
  );
  return result;
}
