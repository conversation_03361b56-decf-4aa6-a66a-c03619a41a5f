import 'dart:convert';

import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;

const String MAPBOX_ACCESS_TOKEN =
    "pk.eyJ1Ijoiam9zZWx1aXozMWR3IiwiYSI6ImNtYmI3a2gwOTBjYm4ybm9rNzdrMzRncGUifQ.t9nbM_QAQyG3k_f02F6MkQ";

Future<List<LatLng>> getRouteCoordinates(
    LatLng origin, LatLng destination) async {
  final url = Uri.parse(
    "https://api.mapbox.com/directions/v5/mapbox/driving/"
    "${origin.longitude},${origin.latitude};"
    "${destination.longitude},${destination.latitude}"
    "?geometries=geojson&access_token=$MAPBOX_ACCESS_TOKEN",
  );

  final response = await http.get(url);
  if (response.statusCode == 200) {
    final data = json.decode(response.body);
    final coordinates = data['routes'][0]['geometry']['coordinates'];
    return coordinates
        .map<LatLng>((coord) => LatLng(coord[1], coord[0]))
        .toList();
  } else {
    throw Exception("Erro ao obter rota: ${response.statusCode}");
  }
}

Future<RouteInfo> getRouteInfo(LatLng origin, LatLng destination) async {
  final url = Uri.parse(
    "https://api.mapbox.com/directions/v5/mapbox/driving/"
    "${origin.longitude},${origin.latitude};"
    "${destination.longitude},${destination.latitude}"
    "?geometries=geojson&access_token=$MAPBOX_ACCESS_TOKEN",
  );

  final response = await http.get(url);
  if (response.statusCode == 200) {
    final data = json.decode(response.body)['routes'][0];
    final coordinates = data['geometry']['coordinates'];
    return RouteInfo(
      route: coordinates
          .map<LatLng>((coord) => LatLng(coord[1], coord[0]))
          .toList(),
      distance: data['distance'].toDouble(),
      duration: data['duration'].toDouble(),
    );
  } else {
    throw Exception("Erro ao obter rota: ${response.statusCode}");
  }
}

class RouteInfo {
  final List<LatLng> route;
  final double distance;
  final double duration;

  RouteInfo({
    required this.route,
    required this.distance,
    required this.duration,
  });
}
