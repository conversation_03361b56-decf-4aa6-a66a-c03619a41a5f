import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class MapSelectionScreen extends StatelessWidget {
  final double latitude;
  final double longitude;

  const MapSelectionScreen({
    Key? key,
    required this.latitude,
    required this.longitude,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Mapa',
          style: TextStyle(color: Colors.black),
        ),
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: Text(
              'Escolha o aplicativo que irá utilizar em suas corridas',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xff425799),
              ),
            ),
          ),
          SizedBox(height: 40),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildMapOption(
                context,
                'Google Maps',
                'assets/images/icone_googlemaps.png',
                () => _launchGoogleMaps(latitude, longitude),
              ),
              _buildMapOption(
                context,
                'Apple Maps',
                'assets/images/icone_apple_mapas.png',
                () => _launchAppleMaps(latitude, longitude),
              ),
              _buildMapOption(
                context,
                'Waze',
                'assets/images/waze.png',
                () => _launchWaze(latitude, longitude),
              ),
            ],
          ),
          SizedBox(height: 60),
        ],
      ),
    );
  }

  Widget _buildMapOption(
    BuildContext context,
    String name,
    String iconPath,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Image.asset(
            iconPath,
            width: 60,
            height: 60,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: 60,
                height: 60,
                color: Colors.grey[300],
                child: Icon(
                  name == 'Google Maps'
                      ? Icons.map
                      : name == 'Apple Maps'
                          ? Icons.map_outlined
                          : Icons.navigation,
                  size: 30,
                  color: Colors.black54,
                ),
              );
            },
          ),
          SizedBox(height: 8),
        ],
      ),
    );
  }

  void _launchGoogleMaps(double latitude, double longitude) async {
    final url =
        'https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude&travelmode=driving';
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  void _launchAppleMaps(double latitude, double longitude) async {
    final url = 'https://maps.apple.com/?daddr=$latitude,$longitude&dirflg=d';
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  void _launchWaze(double latitude, double longitude) async {
    final url = 'https://waze.com/ul?ll=$latitude,$longitude&navigate=yes';
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
}
