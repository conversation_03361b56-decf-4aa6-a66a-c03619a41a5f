import 'package:emartdriver/model/OrderModel.dart';
import 'package:flutter/material.dart';

class OrderBottomSheet extends StatefulWidget {
  final OrderModel order;
  final VoidCallback onAccept;
  final double distanceInKm;
  final double durationInMinutes;
  final VoidCallback onDecline;
  final bool pedidoAceito;

  const OrderBottomSheet({
    required this.order,
    required this.onAccept,
    required this.distanceInKm,
    required this.durationInMinutes,
    required this.pedidoAceito,
    Key? key,
    required this.onDecline,
  });

  @override
  _OrderBottomSheetState createState() => _OrderBottomSheetState();
}

class _OrderBottomSheetState extends State<OrderBottomSheet> {
  int timeLeft = 20;
  double progress = 1.0;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 20,
            ),
            GestureDetector(
              onTap: () {},
              child: Container(
                width: 50,
                height: 5,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            )
          ],
        ),
        const SizedBox(height: 30),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            InfoItem(
              icon: Icons.attach_money,
              value: "R\$ ${widget.order.payValueDriver}",
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: Color(0xff425799),
              ),
              width: 2,
              height: 30,
            ),
            InfoItem(
              icon: Icons.turn_right_sharp,
              value: "${widget.distanceInKm.toStringAsFixed(2)} km",
            ),
          ],
        ),
        Divider(thickness: 1.5, color: Color(0xff425799)),
        if (widget.order.has_return) ...[
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Color.fromARGB(195, 201, 212, 247).withOpacity(0.2),
            ),
            width: MediaQuery.of(context).size.width,
            height: 70,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset('assets/images/caixa.png', height: 30, width: 30),
                const SizedBox(width: 8),
                const Text(
                  "Este pedido pode ser devolvido",
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          )
        ],
        const SizedBox(height: 8),
        Expanded(child: Container()),
        Padding(
          padding: const EdgeInsets.all(18.0),
          child: widget.pedidoAceito
              ? Container(
                  alignment: Alignment.center,
                  height: 50,
                  width: MediaQuery.of(context).size.width * 0.7,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    color: Colors.green,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.check_circle_outline_rounded,
                        color: Colors.white,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        "Corrida aceita com sucesso!",
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ))
              : Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  spacing: 20,
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        onPressed: () {
                          widget.onDecline();
                        },
                        child: const Text(
                          "Recusar",
                          style: TextStyle(fontSize: 14, color: Colors.white),
                        ),
                      ),
                    ),
                    Expanded(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        onPressed: () {
                          widget.onAccept();
                        },
                        child: const Text(
                          "Aceitar",
                          style: TextStyle(fontSize: 14, color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                ),
        ),
      ],
    );
  }
}

class InfoItem extends StatelessWidget {
  final String value;
  final IconData icon;

  const InfoItem({required this.value, required this.icon});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Color(0xff425799)),
            padding: const EdgeInsets.all(5),
            alignment: Alignment.center,
            child: Icon(icon, size: 19, color: Colors.white)),
        const SizedBox(width: 4),
        Text(value,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
      ],
    );
  }
}
