import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/AddressModel.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

Future<void> showStoreInfoDialog(
    BuildContext contextParam, OrderModel order) async {
  try {
    // Preparar os dados do endereço
    bool isDeliveryInProgress = order.status == OrderStatus.driverOnTheWay;
    late AddressModel locationAddress;
    late String locationName;
    late String buttonText;
    late String progressText;
    late double progressValue;

    // Configurar com base no status do pedido
    if (isDeliveryInProgress) {
      if (order.author.shippingAddress == null ||
          order.author.shippingAddress!.isEmpty) {
        log("Erro: endereço do cliente é nulo ou vazio");
        return;
      }

      locationAddress = order.author.shippingAddress!.firstWhere(
          (addr) => addr.isDefault == true,
          orElse: () => order.author.shippingAddress!.first);

      locationName = "${order.author.firstName} ${order.author.lastName}";

      buttonText = "Confirmar Entrega";
      progressText = "Você está a caminho da Entrega";
      progressValue = 0.7;
    } else {
      if (order.vendor.address_store == null) {
        log("Erro: address_store é nulo");
        return;
      }

      locationAddress = AddressModel(
          logradouro: order.vendor.address_store!.logradouro,
          numero: order.vendor.address_store!.numero,
          bairro: order.vendor.address_store!.bairro,
          cidade: order.vendor.address_store!.cidade,
          complemento: order.vendor.address_store!.complemento,
          location: order.vendor.address_store!.location);

      locationName = order.vendor.title;

      buttonText = "Cheguei na coleta";
      progressText = "Você está a caminho da Coleta";
      progressValue = 0.4;
    }

    // Preparar as linhas de endereço
    final List<String> fullAddressLines = [];

    final String logradouro = locationAddress.logradouro ?? "";
    final String numero = locationAddress.numero ?? "";
    final String bairro = locationAddress.bairro ?? "";
    final String cidade = locationAddress.cidade ?? "";
    final String complemento = locationAddress.complemento ?? "";
    final String referencia = locationAddress.referencia ?? "";

    if (logradouro.isNotEmpty || numero.isNotEmpty) {
      fullAddressLines
          .add("$logradouro${numero.isNotEmpty ? ", $numero" : ''}");
    }

    if (bairro.isNotEmpty) {
      fullAddressLines.add(bairro);
    }

    if (cidade.isNotEmpty) {
      fullAddressLines.add(cidade);
    }

    if (complemento.isNotEmpty) {
      fullAddressLines.add("Complemento: $complemento");
    }

    if (referencia.isNotEmpty) {
      fullAddressLines.add("Referência: $referencia");
    }

    if (!isDeliveryInProgress && fullAddressLines.isNotEmpty) {
      fullAddressLines.add("Ao lado da Agência Santander");
    }

    if (fullAddressLines.isEmpty) {
      log("Erro: Não foi possível criar linhas de endereço válidas");
      return;
    }

    // Mostrar o diálogo com o widget StatefulWidget
    await showModalBottomSheet(
      context: contextParam,
      isScrollControlled: true,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(contextParam).size.height * 0.85,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return StoreInfoBottomSheet(
          order: order,
          isDeliveryInProgress: isDeliveryInProgress,
          locationAddress: locationAddress,
          locationName: locationName,
          fullAddressLines: fullAddressLines,
          buttonText: buttonText,
          progressText: progressText,
          progressValue: progressValue,
          parentContext: contextParam,
        );
      },
    );
  } catch (e) {
    log("Erro: $e");
  }
}

// Widget StatefulWidget para o bottom sheet
class StoreInfoBottomSheet extends StatefulWidget {
  final OrderModel order;
  final bool isDeliveryInProgress;
  final AddressModel locationAddress;
  final String locationName;
  final List<String> fullAddressLines;
  final String buttonText;
  final String progressText;
  final double progressValue;
  final BuildContext parentContext;

  const StoreInfoBottomSheet({
    Key? key,
    required this.order,
    required this.isDeliveryInProgress,
    required this.locationAddress,
    required this.locationName,
    required this.fullAddressLines,
    required this.buttonText,
    required this.progressText,
    required this.progressValue,
    required this.parentContext,
  }) : super(key: key);

  @override
  _StoreInfoBottomSheetState createState() => _StoreInfoBottomSheetState();
}

class _StoreInfoBottomSheetState extends State<StoreInfoBottomSheet> {
  late TextEditingController codeController;
  late StreamController<ErrorAnimationType> errorController;
  String currentText = "";
  bool isButtonEnabled = false;

  @override
  void initState() {
    super.initState();
    codeController = TextEditingController();
    errorController = StreamController<ErrorAnimationType>();

    // Se for entrega, o botão já começa habilitado
    isButtonEnabled = widget.isDeliveryInProgress;
  }

  @override
  void dispose() {
    // Usar try-catch para evitar o erro de controller já descartado
    try {
      codeController.dispose();
    } catch (e) {
      // Ignora o erro se o controller já foi descartado
      print("Erro ao descartar codeController: $e");
    }

    try {
      if (!errorController.isClosed) {
        errorController.close();
      }
    } catch (e) {
      // Ignora o erro se o controller já foi fechado
      print("Erro ao fechar errorController: $e");
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 20.0,
        right: 20.0,
        top: 10.0,
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 20),
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 10),
                      Text(
                        widget.locationName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...widget.fullAddressLines.map(
                        (line) => Text(
                          line,
                          style: const TextStyle(
                              fontSize: 14, color: Colors.black87),
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 14.0,
                    vertical: 14.0,
                  ),
                  child: GestureDetector(
                    onTap: () async {
                      await Navigator.push<bool>(
                        context,
                        MaterialPageRoute(
                          builder: (context) => MapSelectionScreen(
                            latitude: widget.locationAddress.location!.latitude,
                            longitude:
                                widget.locationAddress.location!.longitude,
                          ),
                        ),
                      );
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: const Color.fromARGB(255, 255, 132, 0),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.location_on,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                        const SizedBox(height: 6),
                        const Text(
                          "Mapa",
                          style: TextStyle(
                            color: Colors.orange,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Container(
              padding: const EdgeInsets.only(top: 20),
              alignment: Alignment.center,
              width: double.infinity,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Card(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      color: Colors.white,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 10),
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16.0,
                              vertical: 6.0,
                            ),
                            child: Text(
                              widget.progressText,
                              style: const TextStyle(fontSize: 12),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16.0,
                            ),
                            child: LinearProgressIndicator(
                              minHeight: 3,
                              value: widget.progressValue,
                              borderRadius: BorderRadius.circular(10),
                              valueColor: const AlwaysStoppedAnimation<Color>(
                                  Color.fromARGB(255, 255, 132, 0)),
                              backgroundColor: Colors.grey[300],
                            ),
                          ),
                          const SizedBox(height: 10),
                        ],
                      ),
                    ),
                    // Mostrar o campo de código PIN apenas quando não estiver na tela de entrega
                    if (!widget.isDeliveryInProgress) ...[
                      const Column(
                        children: [
                          Text(
                            textAlign: TextAlign.center,
                            'Ao chegar na coleta forneça o código para retirar o pedido',
                            style: TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: PinCodeTextField(
                          appContext: context,
                          length: 4,
                          obscureText: false,
                          controller: codeController,
                          keyboardType: TextInputType.number,
                          animationType: AnimationType.fade,
                          errorAnimationController: errorController,
                          pinTheme: PinTheme(
                            shape: PinCodeFieldShape.box,
                            borderRadius: BorderRadius.circular(8),
                            fieldHeight: 50,
                            fieldWidth: 45,
                            activeFillColor: Colors.white,
                            inactiveFillColor: Colors.white,
                            selectedFillColor: Colors.white,
                            activeColor: const Color.fromARGB(255, 255, 132, 0),
                            inactiveColor: Colors.grey.shade300,
                            selectedColor:
                                const Color.fromARGB(255, 255, 132, 0),
                            errorBorderColor: Colors.redAccent,
                          ),
                          animationDuration: const Duration(milliseconds: 300),
                          enableActiveFill: true,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                          onChanged: (value) {
                            currentText = value;

                            final correctCode =
                                widget.order.storeConfirmationCode;

                            setState(() {
                              isButtonEnabled = (value.length == 4 &&
                                  value == correctCode.toString());
                            });
                          },
                          onCompleted: (value) {
                            currentText = value;
                            print("Código completo: $value");
                          },
                          beforeTextPaste: (text) {
                            return text?.contains(RegExp(r'^[0-9]+$')) ?? false;
                          },
                        ),
                      ),
                    ],

                    const SizedBox(height: 20),
                    Wrap(
                      alignment: WrapAlignment.center,
                      runSpacing: 10,
                      children: [
                        if (!widget.isDeliveryInProgress)
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              backgroundColor: Colors.red,
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(
                                vertical: 12.0,
                              ),
                              child: Text(
                                "Cancelar coleta",
                                style: TextStyle(
                                    fontSize: 16, color: Colors.white),
                              ),
                            ),
                            onPressed: () async {
                              final currentContext = context;

                              // Confirmar se realmente deseja cancelar
                              final confirmarCancelamento =
                                  await showDialog<bool>(
                                context: currentContext,
                                builder: (BuildContext context) {
                                  return AlertDialog(
                                    title: const Text('Cancelar coleta'),
                                    content: const Text(
                                        'Tem certeza que deseja cancelar a coleta? O pedido voltará para o status "motorista pendente".'),
                                    actions: [
                                      TextButton(
                                        onPressed: () =>
                                            Navigator.of(context).pop(false),
                                        child: const Text('Não'),
                                      ),
                                      TextButton(
                                        onPressed: () =>
                                            Navigator.of(context).pop(true),
                                        style: TextButton.styleFrom(
                                            foregroundColor: Colors.red),
                                        child: const Text('Sim'),
                                      ),
                                    ],
                                  );
                                },
                              );

                              if (confirmarCancelamento == true) {
                                try {
                                  // Atualizar o status do pedido para "driverPending"
                                  final docRef = FirebaseFirestore.instance
                                      .collection(ORDERS)
                                      .doc(widget.order.id);

                                  await docRef.update({
                                    'status':
                                        OrderStatus.driverPending.description,
                                    'entregador_id': null,
                                  });

                                  // fechar telas antes de mostrar o snackbar
                                  if (currentContext.mounted) {
                                    Navigator.pop(currentContext);
                                  }
                                } catch (e) {
                                  log("Erro ao cancelar coleta: $e");
                                  ScaffoldMessenger.of(currentContext)
                                      .showSnackBar(
                                    SnackBar(
                                      content:
                                          Text("Erro ao cancelar coleta: $e"),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              }
                            },
                          ),
                        const SizedBox(width: 10),
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            backgroundColor: isButtonEnabled
                                ? const Color.fromARGB(255, 255, 132, 0)
                                : Colors.grey,
                          ),
                          // Desabilitar o botão apenas se não estiver na tela de entrega e o código não estiver correto
                          onPressed: isButtonEnabled
                              ? () async {
                                  final currentContext = context;

                                  if (widget.isDeliveryInProgress) {
                                    Navigator.pop(currentContext);
                                    Navigator.push(
                                      currentContext,
                                      MaterialPageRoute(
                                        builder: (context) => EnderecoCliente(
                                            order: widget.order),
                                      ),
                                    );
                                  } else {
                                    final enteredCode =
                                        codeController.text.trim();
                                    final correctCode = widget
                                        .order.storeConfirmationCode
                                        .toString();

                                    if (enteredCode.isEmpty) {
                                      errorController
                                          .add(ErrorAnimationType.shake);
                                      ScaffoldMessenger.of(currentContext)
                                          .showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                              "Por favor, digite o código de confirmação"),
                                          backgroundColor: Colors.orange,
                                        ),
                                      );
                                      return;
                                    } else if (enteredCode != correctCode) {
                                      errorController
                                          .add(ErrorAnimationType.shake);
                                      ScaffoldMessenger.of(currentContext)
                                          .showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                              "Código incorreto. Tente novamente."),
                                          backgroundColor: Colors.red,
                                        ),
                                      );
                                      return;
                                    }

                                    final result = await coletarPedido(
                                        currentContext, widget.order);

                                    if (result == true) {
                                      if (currentContext.mounted) {
                                        final parentContext =
                                            widget.parentContext;

                                        Navigator.pop(currentContext);
                                        Navigator.pop(parentContext);

                                        await Future.delayed(
                                            const Duration(seconds: 1),
                                            () async {
                                          if (parentContext.mounted) {
                                            try {
                                              final docRef = FirebaseFirestore
                                                  .instance
                                                  .collection(ORDERS)
                                                  .doc(widget.order.id);

                                              final docSnapshot =
                                                  await docRef.get();

                                              if (docSnapshot.exists) {
                                                final updatedOrder =
                                                    OrderModel.fromJson(
                                                        docSnapshot.data()
                                                            as Map<String,
                                                                dynamic>);

                                                showStoreInfoDialog(
                                                    parentContext,
                                                    updatedOrder);
                                              }
                                            } catch (e) {
                                              log("Erro ao buscar pedido atualizado: $e");
                                            }
                                          }
                                        });
                                      }
                                    }
                                  }
                                }
                              : null,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16.0,
                              vertical: 12.0,
                            ),
                            child: Text(
                              widget.buttonText,
                              style: const TextStyle(
                                  fontSize: 16, color: Colors.white),
                            ),
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }
}
