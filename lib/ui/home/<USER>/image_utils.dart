import 'dart:developer';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class ImageUtils {
  static final ImageUtils _instance = ImageUtils._internal();
  factory ImageUtils() => _instance;
  ImageUtils._internal();

  /// Load custom marker icon from assets
  Future<BitmapDescriptor?> loadCustomMarkerIcon({
    required String assetPath,
    int width = 40,
    int height = 40,
  }) async {
    try {
      final Uint8List markerIcon = await getBytesFromAsset(assetPath, width);
      return BitmapDescriptor.bytes(markerIcon);
    } catch (e) {
      log("Error loading custom marker icon from $assetPath: $e");
      return null;
    }
  }

  /// Convert asset image to bytes with specified dimensions
  Future<Uint8List> getBytesFromAsset(String path, int width,
      {int? height}) async {
    try {
      ByteData data = await rootBundle.load(path);
      ui.Codec codec = await ui.instantiateImageCodec(
        data.buffer.asUint8List(),
        targetWidth: width,
        targetHeight: height,
      );
      ui.FrameInfo fi = await codec.getNextFrame();
      return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
          .buffer
          .asUint8List();
    } catch (e) {
      log("Error converting asset to bytes: $e");
      rethrow;
    }
  }

  /// Load delivery person marker icon
  Future<BitmapDescriptor> loadDeliveryPersonIcon() async {
    try {
      final customIcon = await loadCustomMarkerIcon(
        assetPath: 'assets/images/motoentregador.png',
        width: 40,
      );

      return customIcon ??
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    } catch (e) {
      log("Error loading delivery person icon: $e");
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    }
  }

  /// Load store marker icon
  Future<BitmapDescriptor> loadStoreIcon() async {
    try {
      final customIcon = await loadCustomMarkerIcon(
        assetPath: 'assets/images/store_marker.png',
        width: 35,
      );

      return customIcon ??
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    } catch (e) {
      log("Error loading store icon: $e");
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    }
  }

  /// Load client marker icon
  Future<BitmapDescriptor> loadClientIcon() async {
    try {
      final customIcon = await loadCustomMarkerIcon(
        assetPath: 'assets/images/client_marker.png',
        width: 35,
      );

      return customIcon ??
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
    } catch (e) {
      log("Error loading client icon: $e");
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
    }
  }

  /// Create colored marker icon
  BitmapDescriptor createColoredMarker(double hue) {
    return BitmapDescriptor.defaultMarkerWithHue(hue);
  }

  /// Get marker icon based on order count
  BitmapDescriptor getMarkerIconByOrderCount(int orderCount) {
    if (orderCount == 1) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    } else if (orderCount <= 3) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange);
    } else {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
    }
  }

  /// Get marker icon based on order status
  BitmapDescriptor getMarkerIconByStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueYellow);
      case 'accepted':
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
      case 'in_progress':
        return BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueOrange);
      case 'delivered':
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
      case 'cancelled':
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
      default:
        return BitmapDescriptor.defaultMarker;
    }
  }

  /// Get marker icon based on distance
  BitmapDescriptor getMarkerIconByDistance(double distanceKm) {
    if (distanceKm <= 1.0) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    } else if (distanceKm <= 3.0) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueYellow);
    } else if (distanceKm <= 5.0) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange);
    } else {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
    }
  }

  /// Resize image bytes
  Future<Uint8List> resizeImageBytes(
    Uint8List imageBytes,
    int targetWidth,
    int targetHeight,
  ) async {
    try {
      ui.Codec codec = await ui.instantiateImageCodec(
        imageBytes,
        targetWidth: targetWidth,
        targetHeight: targetHeight,
      );
      ui.FrameInfo fi = await codec.getNextFrame();
      return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
          .buffer
          .asUint8List();
    } catch (e) {
      log("Error resizing image bytes: $e");
      rethrow;
    }
  }

  /// Create custom marker with text overlay
  Future<BitmapDescriptor> createMarkerWithText({
    required String text,
    required double hue,
    int width = 100,
    int height = 100,
  }) async {
    try {
      final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(pictureRecorder);
      final Paint paint = Paint()..color = _getColorFromHue(hue);

      // Draw circle background
      canvas.drawCircle(
        Offset(width / 2, height / 2),
        width / 2,
        paint,
      );

      // Draw text
      final textSpan = TextSpan(
        text: text,
        style: TextStyle(
          color: const ui.Color(0xFFFFFFFF),
          fontSize: width * 0.3,
          fontWeight: FontWeight.bold,
        ),
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: ui.TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          (width - textPainter.width) / 2,
          (height - textPainter.height) / 2,
        ),
      );

      final ui.Picture picture = pictureRecorder.endRecording();
      final ui.Image image = await picture.toImage(width, height);
      final ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);

      return BitmapDescriptor.bytes(byteData!.buffer.asUint8List());
    } catch (e) {
      log("Error creating marker with text: $e");
      return BitmapDescriptor.defaultMarkerWithHue(hue);
    }
  }

  /// Convert hue to Color
  ui.Color _getColorFromHue(double hue) {
    return HSVColor.fromAHSV(1.0, hue, 1.0, 1.0).toColor();
  }

  /// Load and cache multiple marker icons
  Future<Map<String, BitmapDescriptor>> loadAllMarkerIcons() async {
    Map<String, BitmapDescriptor> icons = {};

    try {
      // Load delivery person icon
      icons['delivery_person'] = await loadDeliveryPersonIcon();

      // Load store icon
      icons['store'] = await loadStoreIcon();

      // Load client icon
      icons['client'] = await loadClientIcon();

      // Create status-based icons
      icons['pending'] = getMarkerIconByStatus('pending');
      icons['accepted'] = getMarkerIconByStatus('accepted');
      icons['in_progress'] = getMarkerIconByStatus('in_progress');
      icons['delivered'] = getMarkerIconByStatus('delivered');
      icons['cancelled'] = getMarkerIconByStatus('cancelled');

      log("Successfully loaded ${icons.length} marker icons");
    } catch (e) {
      log("Error loading marker icons: $e");
    }

    return icons;
  }

  /// Check if asset exists
  Future<bool> assetExists(String assetPath) async {
    try {
      await rootBundle.load(assetPath);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get default fallback icon
  BitmapDescriptor getDefaultIcon() {
    return BitmapDescriptor.defaultMarker;
  }

  /// Create gradient marker
  Future<BitmapDescriptor> createGradientMarker({
    required List<ui.Color> colors,
    int width = 60,
    int height = 60,
  }) async {
    try {
      final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(pictureRecorder);

      final Paint paint = Paint()
        ..shader = ui.Gradient.linear(
          const Offset(0, 0),
          Offset(width.toDouble(), height.toDouble()),
          colors,
        );

      canvas.drawCircle(
        Offset(width / 2, height / 2),
        width / 2,
        paint,
      );

      final ui.Picture picture = pictureRecorder.endRecording();
      final ui.Image image = await picture.toImage(width, height);
      final ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);

      return BitmapDescriptor.bytes(byteData!.buffer.asUint8List());
    } catch (e) {
      log("Error creating gradient marker: $e");
      return getDefaultIcon();
    }
  }
}
