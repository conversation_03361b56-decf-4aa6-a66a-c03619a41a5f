# Serviços Componentizados - HomeScreen

Este diretório contém os serviços extraídos da HomeScreen.dart para melhorar a organização e reutilização do código.

## Estrutura dos Serviços

### 1. OrderService
**Responsabilidade**: Gerenciamento de operações com pedidos no Firebase

**Principais métodos**:
- `checkForAcceptedOrder()` - Verifica pedidos aceitos
- `acceptOrder(orderId)` - Aceita um novo pedido
- `listenToOrder(orderId, callbacks)` - Escuta mudanças em um pedido
- `getAvailableOrders()` - Busca pedidos disponíveis
- `confirmReturnOrder(orderId)` - Confirma devolução de pedido
- `groupOrdersByStore()` - Agrupa pedidos por loja

**Exemplo de uso**:
```dart
final orderService = OrderService();

// Verificar pedidos aceitos
String? acceptedOrderId = await orderService.checkForAcceptedOrder();

// Aceitar um pedido
OrderModel? order = await orderService.acceptOrder('order_id');

// Escutar mudanças em um pedido
orderService.listenToOrder(
  'order_id',
  onOrderUpdate: (order) => print('Order updated: ${order.id}'),
  onOrderNotFound: () => print('Order not found'),
);
```

### 2. MapService
**Responsabilidade**: Operações do mapa e marcadores

**Principais métodos**:
- `zoomIn(controller)` - Aumenta zoom do mapa
- `zoomOut(controller)` - Diminui zoom do mapa
- `centerOnLocation(controller, position)` - Centraliza mapa em posição
- `createStoreMarkers()` - Cria marcadores de lojas
- `createDestinationMarker()` - Cria marcador de destino
- `createRoutePolyline()` - Cria linha de rota

**Exemplo de uso**:
```dart
final mapService = MapService();

// Controles de zoom
mapService.zoomIn(_mapController);
mapService.zoomOut(_mapController);

// Centralizar mapa
mapService.centerOnLocation(_mapController, currentPosition);

// Criar marcadores
Map<String, Marker> storeMarkers = mapService.createStoreMarkers(
  ordersByStore: ordersByStore,
  onMarkerTap: (orders) => _showMultipleOrdersDialog(orders),
  getLatLng: _getLatLng,
);
```

### 3. RouteService
**Responsabilidade**: Cálculos de rota e distância

**Principais métodos**:
- `calculateDistance(lat1, lon1, lat2, lon2)` - Calcula distância entre pontos
- `getLatLng(geoPoint)` - Converte GeoPoint para LatLng
- `getRouteCoordinates(origin, destination)` - Obtém coordenadas da rota
- `getRouteInfo(origin, destination)` - Obtém informações da rota
- `calculateBearing(start, end)` - Calcula direção entre pontos
- `formatDistance(distanceInKm)` - Formata distância para exibição

**Exemplo de uso**:
```dart
final routeService = RouteService();

// Calcular distância
double distance = routeService.calculateDistance(
  currentLat, currentLng, 
  destinationLat, destinationLng
);

// Obter coordenadas da rota
List<LatLng> routePoints = await routeService.getRouteCoordinates(
  origin, destination
);

// Formatar distância
String formattedDistance = routeService.formatDistance(distance);
```

### 4. NotificationService
**Responsabilidade**: Gerenciamento de notificações e mensagens

**Principais métodos**:
- `showNewOrderNotification()` - Exibe notificação de novo pedido
- `showOrderAcceptedNotification()` - Exibe dialog de pedido aceito
- `showSuccessMessage()` - Exibe mensagem de sucesso
- `showErrorMessage()` - Exibe mensagem de erro
- `showConfirmationDialog()` - Exibe dialog de confirmação

**Exemplo de uso**:
```dart
final notificationService = NotificationService();

// Mostrar mensagem de sucesso
notificationService.showSuccessMessage(
  context: context,
  message: 'Pedido aceito com sucesso!',
);

// Mostrar dialog de confirmação
bool? confirmed = await notificationService.showConfirmationDialog(
  context: context,
  title: 'Confirmar ação',
  message: 'Deseja realmente continuar?',
);
```

## Utilitários

### 1. ValidationUtils
**Responsabilidade**: Validações e verificações

**Principais métodos**:
- `validateUserOnlineStatus()` - Valida se usuário está online
- `validateCurrentPosition()` - Valida se posição atual existe
- `validateOrderData()` - Valida dados do pedido
- `validateOrderStatus()` - Valida status do pedido
- `validateDistance()` - Valida restrições de distância

### 2. ImageUtils
**Responsabilidade**: Utilitários para imagens e ícones

**Principais métodos**:
- `loadCustomMarkerIcon()` - Carrega ícone personalizado
- `loadDeliveryPersonIcon()` - Carrega ícone do entregador
- `getMarkerIconByOrderCount()` - Ícone baseado na quantidade de pedidos
- `createMarkerWithText()` - Cria marcador com texto

## Como Usar

### 1. Importar os serviços:
```dart
import 'services/index.dart';
import 'utils/index.dart';
```

### 2. Instanciar os serviços:
```dart
class _HomeScreenState extends State<HomeScreen> {
  final OrderService _orderService = OrderService();
  final MapService _mapService = MapService();
  final RouteService _routeService = RouteService();
  final NotificationService _notificationService = NotificationService();
  final ValidationUtils _validationUtils = ValidationUtils();
  final ImageUtils _imageUtils = ImageUtils();
  
  // ... resto do código
}
```

### 3. Usar os serviços nas funções:
```dart
void _zoomIn() {
  _mapService.zoomIn(_mapController);
}

double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
  return _routeService.calculateDistance(lat1, lon1, lat2, lon2);
}

void _showSuccessMessage(String message) {
  _notificationService.showSuccessMessage(
    context: context,
    message: message,
  );
}
```

## Benefícios da Componentização

1. **Separação de Responsabilidades**: Cada serviço tem uma função específica
2. **Reutilização**: Serviços podem ser usados em outras telas
3. **Testabilidade**: Cada serviço pode ser testado independentemente
4. **Manutenibilidade**: Mudanças isoladas em cada serviço
5. **Organização**: Código mais limpo e estruturado
6. **Singleton Pattern**: Instância única de cada serviço para eficiência

## Padrões Utilizados

- **Singleton**: Cada serviço é um singleton para garantir uma única instância
- **Service Layer**: Separação da lógica de negócio da UI
- **Dependency Injection**: Serviços podem ser facilmente substituídos para testes
- **Error Handling**: Tratamento de erros centralizado em cada serviço
