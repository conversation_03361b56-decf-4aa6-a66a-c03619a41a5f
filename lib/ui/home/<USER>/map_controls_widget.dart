import 'package:flutter/material.dart';

class MapControlsWidget extends StatelessWidget {
  final VoidCallback onZoomIn;
  final VoidCallback onZoomOut;
  final VoidCallback onCenterLocation;
  final VoidCallback onReloadMap;

  const MapControlsWidget({
    super.key,
    required this.onZoomIn,
    required this.onZoomOut,
    required this.onCenterLocation,
    required this.onReloadMap,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      right: 5,
      top: 6,
      child: Column(
        children: [
          FloatingActionButton(
            heroTag: 'zoomInButton',
            mini: false,
            backgroundColor: Colors.white,
            onPressed: onZoomIn,
            child: const Icon(
              Icons.zoom_in_outlined,
              color: Color(0xff425799),
            ),
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            heroTag: "zoomOutButton",
            mini: false,
            backgroundColor: Colors.white,
            onPressed: onZoomOut,
            child: const Icon(
              Icons.zoom_out_outlined,
              color: Color(0xff425799),
            ),
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            heroTag: "centerLocationButton",
            mini: false,
            backgroundColor: Colors.white,
            onPressed: onCenterLocation,
            child: const Icon(
              Icons.my_location,
              color: Color(0xff425799),
            ),
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            heroTag: "reloadMapButton",
            mini: false,
            backgroundColor: Colors.white,
            onPressed: onReloadMap,
            child: const Icon(
              Icons.refresh,
              color: Color(0xff425799),
            ),
          ),
        ],
      ),
    );
  }
}
