import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class ValidationUtils {
  static final ValidationUtils _instance = ValidationUtils._internal();
  factory ValidationUtils() => _instance;
  ValidationUtils._internal();

  /// Validate if user is online before processing orders
  bool validateUserOnlineStatus(bool isOnline, String operation) {
    if (!isOnline) {
      log("User is offline, not performing $operation");
      return false;
    }
    return true;
  }

  /// Validate current position exists
  bool validateCurrentPosition(LatLng? currentPosition, String operation) {
    if (currentPosition == null) {
      log("Current position is null, cannot perform $operation");
      return false;
    }
    return true;
  }

  /// Validate order data completeness
  bool validateOrderData(OrderModel order) {
    try {
      // Check basic order fields
      if (order.id.isEmpty) {
        log("Order validation failed: Missing order ID");
        return false;
      }

      if (order.vendor.id.isEmpty == true) {
        log("Order validation failed: Missing vendor ID");
        return false;
      }

      if (order.vendor.title.isEmpty) {
        log("Order validation failed: Missing vendor title");
        return false;
      }

      // Check vendor address
      if (order.vendor.address_store?.location.geoPoint == null) {
        log("Order validation failed: Missing vendor location");
        return false;
      }

      // Check customer data
      if (order.author.firstName.isEmpty && order.author.lastName.isEmpty) {
        log("Order validation failed: Missing customer name");
        return false;
      }

      // Check shipping address
      if (order.author.shippingAddress == null ||
          order.author.shippingAddress!.isEmpty) {
        log("Order validation failed: Missing shipping address");
        return false;
      }

      // Check if there's at least one valid shipping address
      bool hasValidAddress = order.author.shippingAddress!
          .any((address) => address.location?.geoPoint != null);

      if (!hasValidAddress) {
        log("Order validation failed: No valid shipping address with location");
        return false;
      }

      return true;
    } catch (e) {
      log("Error validating order data: $e");
      return false;
    }
  }

  /// Validate order status for specific operations
  bool validateOrderStatus(
      OrderModel order, List<OrderStatus> allowedStatuses, String operation) {
    try {
      OrderStatus? currentStatus =
          _getOrderStatusFromString(order.status?.description);

      if (currentStatus == null) {
        log("Order status validation failed: Invalid status for $operation");
        return false;
      }

      if (!allowedStatuses.contains(currentStatus)) {
        log("Order status validation failed: Status ${currentStatus.description} not allowed for $operation");
        return false;
      }

      return true;
    } catch (e) {
      log("Error validating order status: $e");
      return false;
    }
  }

  /// Validate distance constraints
  bool validateDistance(
      double distanceKm, double maxDistanceKm, String operation) {
    if (distanceKm > maxDistanceKm) {
      log("Distance validation failed: ${distanceKm.toStringAsFixed(1)} km exceeds limit of $maxDistanceKm km for $operation");
      return false;
    }
    return true;
  }

  /// Validate GeoPoint data
  bool validateGeoPoint(GeoPoint? geoPoint, String fieldName) {
    if (geoPoint == null) {
      log("GeoPoint validation failed: $fieldName is null");
      return false;
    }

    // Check for valid latitude range
    if (geoPoint.latitude < -90 || geoPoint.latitude > 90) {
      log("GeoPoint validation failed: Invalid latitude ${geoPoint.latitude} for $fieldName");
      return false;
    }

    // Check for valid longitude range
    if (geoPoint.longitude < -180 || geoPoint.longitude > 180) {
      log("GeoPoint validation failed: Invalid longitude ${geoPoint.longitude} for $fieldName");
      return false;
    }

    return true;
  }

  /// Validate order acceptance conditions
  bool validateOrderAcceptance(OrderModel order, String currentUserId) {
    // Check if order is already accepted by another driver
    if (order.status?.description == OrderStatus.driverAccepted.description &&
        order.entregador_id != null &&
        order.entregador_id!.isNotEmpty &&
        order.entregador_id != currentUserId) {
      log("Order acceptance validation failed: Order already accepted by another driver");
      return false;
    }

    // Check if order is in correct status for acceptance
    if (order.status?.description != OrderStatus.driverSearching.description) {
      log("Order acceptance validation failed: Order status is ${order.status}, expected ${OrderStatus.driverSearching.description}");
      return false;
    }

    return validateOrderData(order);
  }

  /// Validate return order conditions
  bool validateReturnOrder(OrderModel order, String currentUserId) {
    // Check if order belongs to current driver
    if (order.entregador_id != currentUserId) {
      log("Return order validation failed: Order does not belong to current driver");
      return false;
    }

    // Check if order is in delivered status
    if (order.status?.description != OrderStatus.delivered.description) {
      log("Return order validation failed: Order status is ${order.status}, expected ${OrderStatus.delivered.description}");
      return false;
    }

    // Check if order has return flag
    if (order.has_return != true) {
      log("Return order validation failed: Order does not have return flag");
      return false;
    }

    return true;
  }

  /// Validate map controller state
  bool validateMapController(dynamic mapController, String operation) {
    if (mapController == null) {
      log("Map controller validation failed: Controller is null for $operation");
      return false;
    }
    return true;
  }

  /// Validate network connectivity (placeholder for future implementation)
  bool validateNetworkConnectivity() {
    // TODO: Implement actual network connectivity check
    return true;
  }

  /// Validate Firebase document exists
  bool validateDocumentSnapshot(
      DocumentSnapshot? snapshot, String documentType) {
    if (snapshot == null || !snapshot.exists) {
      log("Document validation failed: $documentType document does not exist");
      return false;
    }

    if (snapshot.data() == null) {
      log("Document validation failed: $documentType document has no data");
      return false;
    }

    return true;
  }

  /// Validate required permissions
  bool validatePermissions(
      List<String> requiredPermissions, List<String> grantedPermissions) {
    for (String permission in requiredPermissions) {
      if (!grantedPermissions.contains(permission)) {
        log("Permission validation failed: Missing permission $permission");
        return false;
      }
    }
    return true;
  }

  /// Validate time constraints
  bool validateTimeConstraints(DateTime? orderTime, int maxHoursOld) {
    if (orderTime == null) {
      log("Time validation failed: Order time is null");
      return false;
    }

    final now = DateTime.now();
    final difference = now.difference(orderTime);

    if (difference.inHours > maxHoursOld) {
      log("Time validation failed: Order is ${difference.inHours} hours old, max allowed is $maxHoursOld");
      return false;
    }

    return true;
  }

  /// Helper method to convert string to OrderStatus
  OrderStatus? _getOrderStatusFromString(String? statusString) {
    if (statusString == null) return null;

    for (OrderStatus status in OrderStatus.values) {
      if (status.description == statusString) {
        return status;
      }
    }
    return null;
  }

  /// Validate coordinates are within reasonable bounds
  bool validateCoordinateBounds(LatLng coordinates, String fieldName) {
    // Check for obviously invalid coordinates (like 0,0 which might be default values)
    if (coordinates.latitude == 0.0 && coordinates.longitude == 0.0) {
      log("Coordinate validation failed: $fieldName appears to be default (0,0)");
      return false;
    }

    // Check for valid latitude range
    if (coordinates.latitude < -90 || coordinates.latitude > 90) {
      log("Coordinate validation failed: Invalid latitude ${coordinates.latitude} for $fieldName");
      return false;
    }

    // Check for valid longitude range
    if (coordinates.longitude < -180 || coordinates.longitude > 180) {
      log("Coordinate validation failed: Invalid longitude ${coordinates.longitude} for $fieldName");
      return false;
    }

    return true;
  }

  /// Validate string is not empty or null
  bool validateNonEmptyString(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      log("String validation failed: $fieldName is null or empty");
      return false;
    }
    return true;
  }

  /// Validate numeric value is within range
  bool validateNumericRange(
      double value, double min, double max, String fieldName) {
    if (value < min || value > max) {
      log("Numeric validation failed: $fieldName value $value is outside range [$min, $max]");
      return false;
    }
    return true;
  }
}
