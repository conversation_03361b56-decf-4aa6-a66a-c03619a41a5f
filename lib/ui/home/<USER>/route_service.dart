import 'dart:developer';
import 'dart:math' as math;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class RouteService {
  static final RouteService _instance = RouteService._internal();
  factory RouteService() => _instance;
  RouteService._internal();

  /// Calculate distance between two points using Haversine formula
  double calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    const double earthRadius = 6371; // Earth radius in kilometers
    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) *
            math.cos(_degreesToRadians(lat2)) *
            math.sin(dLon / 2) *
            math.sin(dLon / 2);

    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    double distance = earthRadius * c;

    return distance;
  }

  /// Convert degrees to radians
  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

  /// Convert GeoPoint to LatLng
  LatLng? getLatLng(GeoPoint? geoPoint) {
    if (geoPoint != null) {
      return LatLng(geoPoint.latitude, geoPoint.longitude);
    }
    return null;
  }

  /// Get route coordinates between two points
  Future<List<LatLng>> getRouteCoordinates(
      LatLng origin, LatLng destination) async {
    try {
      return await getRouteCoordinates(origin, destination);
    } catch (e) {
      log("Error getting route coordinates: $e");
      // Return direct line if route service fails
      return [origin, destination];
    }
  }

  /// Get route information (distance and duration)
  Future<RouteInfo> getRouteInfo(LatLng origin, LatLng destination) async {
    try {
      return await getRouteInfo(origin, destination);
    } catch (e) {
      log("Error getting route info: $e");
      // Return calculated distance if route service fails
      double distance = calculateDistance(
            origin.latitude,
            origin.longitude,
            destination.latitude,
            destination.longitude,
          ) *
          1000; // Convert to meters

      return RouteInfo(
        route: [origin, destination],
        distance: distance,
        duration: distance / 10, // Rough estimate: 10 m/s average speed
      );
    }
  }

  /// Calculate estimated time based on distance
  double calculateEstimatedTime(double distanceInKm) {
    // Assuming average speed of 30 km/h in urban areas
    const double averageSpeedKmh = 30.0;
    return (distanceInKm / averageSpeedKmh) * 60; // Return in minutes
  }

  /// Get multiple route points for optimization
  Future<List<LatLng>> getOptimizedRoute(List<LatLng> waypoints) async {
    if (waypoints.length <= 2) {
      return waypoints;
    }

    try {
      // For now, return waypoints in order
      // In the future, this could implement route optimization algorithms
      List<LatLng> optimizedRoute = [];

      for (int i = 0; i < waypoints.length - 1; i++) {
        final routeSegment =
            await getRouteCoordinates(waypoints[i], waypoints[i + 1]);
        optimizedRoute.addAll(routeSegment);
      }

      return optimizedRoute;
    } catch (e) {
      log("Error optimizing route: $e");
      return waypoints;
    }
  }

  /// Check if point is within delivery radius
  bool isWithinDeliveryRadius(
    LatLng center,
    LatLng point,
    double radiusKm,
  ) {
    double distance = calculateDistance(
      center.latitude,
      center.longitude,
      point.latitude,
      point.longitude,
    );
    return distance <= radiusKm;
  }

  /// Get nearest point from a list of points
  LatLng? getNearestPoint(LatLng reference, List<LatLng> points) {
    if (points.isEmpty) return null;

    LatLng nearest = points.first;
    double minDistance = calculateDistance(
      reference.latitude,
      reference.longitude,
      nearest.latitude,
      nearest.longitude,
    );

    for (LatLng point in points.skip(1)) {
      double distance = calculateDistance(
        reference.latitude,
        reference.longitude,
        point.latitude,
        point.longitude,
      );

      if (distance < minDistance) {
        minDistance = distance;
        nearest = point;
      }
    }

    return nearest;
  }

  /// Calculate bearing between two points
  double calculateBearing(LatLng start, LatLng end) {
    double lat1Rad = _degreesToRadians(start.latitude);
    double lat2Rad = _degreesToRadians(end.latitude);
    double deltaLonRad = _degreesToRadians(end.longitude - start.longitude);

    double y = math.sin(deltaLonRad) * math.cos(lat2Rad);
    double x = math.cos(lat1Rad) * math.sin(lat2Rad) -
        math.sin(lat1Rad) * math.cos(lat2Rad) * math.cos(deltaLonRad);

    double bearingRad = math.atan2(y, x);
    double bearingDeg = bearingRad * (180 / math.pi);

    return (bearingDeg + 360) % 360; // Normalize to 0-360 degrees
  }

  /// Get intermediate point between two coordinates
  LatLng getIntermediatePoint(LatLng start, LatLng end, double fraction) {
    double lat1Rad = _degreesToRadians(start.latitude);
    double lon1Rad = _degreesToRadians(start.longitude);
    double lat2Rad = _degreesToRadians(end.latitude);
    double lon2Rad = _degreesToRadians(end.longitude);

    double deltaLat = lat2Rad - lat1Rad;
    double deltaLon = lon2Rad - lon1Rad;

    double lat = lat1Rad + fraction * deltaLat;
    double lon = lon1Rad + fraction * deltaLon;

    return LatLng(
      lat * (180 / math.pi),
      lon * (180 / math.pi),
    );
  }

  /// Format distance for display
  String formatDistance(double distanceInKm) {
    if (distanceInKm < 1.0) {
      return "${(distanceInKm * 1000).round()} m";
    } else {
      return "${distanceInKm.toStringAsFixed(1)} km";
    }
  }

  /// Format duration for display
  String formatDuration(double durationInMinutes) {
    if (durationInMinutes < 60) {
      return "${durationInMinutes.round()} min";
    } else {
      int hours = (durationInMinutes / 60).floor();
      int minutes = (durationInMinutes % 60).round();
      return "${hours}h ${minutes}min";
    }
  }

  /// Check if two coordinates are approximately equal
  bool areCoordinatesEqual(LatLng coord1, LatLng coord2,
      {double toleranceMeters = 10.0}) {
    double distance = calculateDistance(
          coord1.latitude,
          coord1.longitude,
          coord2.latitude,
          coord2.longitude,
        ) *
        1000; // Convert to meters

    return distance <= toleranceMeters;
  }
}
