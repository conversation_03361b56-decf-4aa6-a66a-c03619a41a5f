import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MapWidget extends StatelessWidget {
  final LatLng? currentPosition;
  final Set<Marker> markers;
  final Set<Polyline> polylines;
  final Function(GoogleMapController) onMapCreated;
  final Function(LatLng) onMapTap;

  const MapWidget({
    super.key,
    required this.currentPosition,
    required this.markers,
    required this.polylines,
    required this.onMapCreated,
    required this.onMapTap,
  });

  @override
  Widget build(BuildContext context) {
    return GoogleMap(
      mapType: MapType.terrain,
      onMapCreated: onMapCreated,
      myLocationEnabled: false,
      myLocationButtonEnabled: false,
      zoomControlsEnabled: false,
      markers: markers,
      polylines: polylines,
      initialCameraPosition: CameraPosition(
        target: currentPosition ?? const LatLng(0, 0),
        zoom: 17,
      ),
      onTap: onMapTap,
    );
  }
}
