import 'dart:async';

import 'package:emartdriver/constants.dart'; // Para COLOR_PRIMARY
import 'package:emartdriver/services/TaEntregueApiService.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class VerifyCodeScreen extends StatefulWidget {
  final String correctCode;
  final String whatsappNumber;
  final Future<void> Function() onVerificationSuccess;

  const VerifyCodeScreen({
    Key? key,
    required this.correctCode,
    required this.whatsappNumber,
    required this.onVerificationSuccess,
  }) : super(key: key);

  @override
  State<VerifyCodeScreen> createState() => _VerifyCodeScreenState();
}

class _VerifyCodeScreenState extends State<VerifyCodeScreen> {
  final TextEditingController _codeController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey();
  late StreamController<ErrorAnimationType> _errorController;
  bool _isButtonEnabled = false;
  bool _isControllerClosed = false;

  @override
  void initState() {
    super.initState();
    _errorController = StreamController<ErrorAnimationType>.broadcast();

    _codeController.addListener(() {
      setState(() {
        _isButtonEnabled = _codeController.text == widget.correctCode;
      });
    });
  }

  @override
  void dispose() {
    _codeController.dispose();
    if (!_isControllerClosed) {
      _errorController.close();
      _isControllerClosed = true;
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text(
          'Verificar Código',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Color(COLOR_PRIMARY),
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 32.0),
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.phonelink_lock,
                    size: 80, color: Color(COLOR_PRIMARY)),
                const SizedBox(height: 24),
                Text(
                  'Confirmação Necessária',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 26,
                    fontWeight: FontWeight.bold,
                    color: Color(COLOR_PRIMARY),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Enviamos um código de 4 dígitos para o seu WhatsApp: ${widget.whatsappNumber}.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                ),
                const SizedBox(height: 32),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: PinCodeTextField(
                    appContext: context,
                    length: 4,
                    controller: _codeController,
                    animationType: AnimationType.scale,
                    errorAnimationController: _errorController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    pinTheme: PinTheme(
                      shape: PinCodeFieldShape.box,
                      borderRadius: BorderRadius.circular(12.0),
                      fieldHeight: 52,
                      fieldWidth: 45,
                      activeFillColor: Colors.white,
                      inactiveFillColor: Colors.grey[200],
                      selectedFillColor: Colors.white,
                      activeColor: Color(COLOR_PRIMARY),
                      inactiveColor: Colors.grey.shade400,
                      selectedColor: Color(COLOR_PRIMARY),
                      borderWidth: 1.3,
                    ),
                    animationDuration: const Duration(milliseconds: 300),
                    enableActiveFill: true,
                    cursorColor: Color(COLOR_PRIMARY),
                    onChanged: (_) {},
                    beforeTextPaste: (text) =>
                        text != null && int.tryParse(text) != null,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Por favor, insira o código.';
                      }
                      if (value.length != 4) {
                        return 'O código deve ter 4 dígitos.';
                      }
                      if (int.tryParse(value) == null) {
                        return 'O código deve conter apenas números.';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(height: 40),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isButtonEnabled
                        ? Color(COLOR_PRIMARY)
                        : Colors.grey.shade400,
                    padding: const EdgeInsets.symmetric(vertical: 18),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    elevation: 5,
                  ),
                  onPressed: _isButtonEnabled
                      ? () async => await widget.onVerificationSuccess()
                      : null,
                  child: const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24.0),
                    child: Text(
                      'Confirmar Código',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                TextButton(
                  onPressed: () {
                    TaEntregueApiService.gerarCodigoEntregador(
                        widget.whatsappNumber);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'Código reenviado com sucesso!',
                          textAlign: TextAlign.center,
                        ),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                  child: Text(
                    'Não recebeu o código? Reenviar',
                    style: TextStyle(
                      color: Color(COLOR_PRIMARY),
                      decoration: TextDecoration.underline,
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
