import 'package:emartdriver/model/VendorModel.dart';
import 'package:emartdriver/services/FirebaseHelper.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

class VincularLojasPage extends StatefulWidget {
  const VincularLojasPage({super.key});

  @override
  State<VincularLojasPage> createState() => _VincularLojasPageState();
}

class _VincularLojasPageState extends State<VincularLojasPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: FutureBuilder<String?>(
                  future: FireStoreUtils.associateCode(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    } else if (snapshot.hasError) {
                      return const Text(
                        "Erro ao gerar código",
                        style: TextStyle(fontSize: 18, color: Colors.red),
                      );
                    } else if (snapshot.hasData) {
                      final codigo = snapshot.data!;
                      return Row(
                        children: [
                          Expanded(
                            child: Text(
                              "Código: $codigo",
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              Share.share(
                                codigo,
                                subject: "Código de associação",
                              );
                            },
                            child: const Icon(Icons.share,
                                color: Colors.deepPurple),
                          ),
                        ],
                      );
                    } else {
                      return Row(
                        children: [
                          const Expanded(
                            child: Text(
                              "Nenhum código disponível",
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              setState(() {});
                            },
                            child: const Icon(Icons.refresh,
                                color: Colors.deepPurple),
                          ),
                        ],
                      );
                    }
                  },
                ),
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              "Lojas associadas",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 5),
            Expanded(
              child: LojasAcossiadas(),
            ),
          ],
        ),
      ),
    );
  }
}

class LojasAcossiadas extends StatefulWidget {
  const LojasAcossiadas({super.key});

  @override
  State<LojasAcossiadas> createState() => _LojasAcossiadasState();
}

class _LojasAcossiadasState extends State<LojasAcossiadas> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<VendorModel>>(
      stream: FireStoreUtils.associatedStores(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else if (snapshot.hasError) {
          return const Center(
            child: Text(
              "Erro ao carregar lojas",
              style: TextStyle(fontSize: 18, color: Colors.red),
            ),
          );
        } else if (snapshot.hasData) {
          final lojas = snapshot.data!;
          if (lojas.isEmpty) {
            return const Center(
              child: Text(
                "Nenhuma loja associada",
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey,
                ),
              ),
            );
          }
          return ListView.builder(
            itemCount: lojas.length,
            itemBuilder: (context, index) {
              final loja = lojas[index];
              return GestureDetector(
                onTap: () async {
                  if (loja.situation.title == "Aceito") {
                    final result = await showDialog(
                      context: context,
                      builder: (context) {
                        return AlertDialog(
                          title: Text("Desvincular loja"),
                          content:
                              Text("Deseja desvincular da loja ${loja.title}?"),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context, false),
                              child: const Text(
                                "CANCELAR",
                                style: TextStyle(color: Colors.grey),
                              ),
                            ),
                            TextButton(
                              onPressed: () => Navigator.pop(context, true),
                              child: const Text(
                                "DESVINCULAR",
                                style: TextStyle(color: Colors.red),
                              ),
                            ),
                          ],
                        );
                      },
                    );
                    if (result == true) {
                      await FireStoreUtils.removeAssociatedStore(loja.id);
                    }
                  } else if (loja.situation.title == "Aguardando aceite") {
                    final result = await showDialog(
                      context: context,
                      builder: (context) {
                        return AlertDialog(
                          title: Text("Vincular loja"),
                          content:
                              Text("Deseja vincular a loja ${loja.title}?"),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context, false),
                              child: const Text(
                                "CANCELAR",
                                style: TextStyle(color: Colors.grey),
                              ),
                            ),
                            TextButton(
                              onPressed: () => Navigator.pop(context, true),
                              child: const Text(
                                "VINCULAR",
                                style: TextStyle(color: Colors.green),
                              ),
                            ),
                          ],
                        );
                      },
                    );
                    if (result == true) {
                      await FireStoreUtils.approveStore(loja.id);
                      setState(() {});
                    }
                  }
                },
                child: Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: ListTile(
                    title: Text(loja.title),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (loja.situation.title == "Aguardando aceite")
                          Text(
                            "CLIQUE PARA APROVAR",
                            style: TextStyle(
                              color: Colors.green,
                              fontSize: 16,
                            ),
                          ),
                        if (loja.situation.title == "Aceito")
                          Text(
                            "CLIQUE PARA DESVINCULAR",
                            style: TextStyle(
                              color: Colors.red,
                              fontSize: 16,
                            ),
                          ),
                        if (loja.situation.title == "Recusado")
                          Text(
                            "LOJA RECUSADA",
                            style: TextStyle(
                              color: Colors.green,
                              fontSize: 16,
                            ),
                          ),
                      ],
                    ),
                    trailing: loja.situation.title == "Aceito"
                        ? const Icon(Icons.check, color: Colors.green)
                        : loja.situation.title == "Aguardando aceite"
                            ? const Icon(Icons.pending, color: Colors.orange)
                            : const Icon(Icons.close, color: Colors.red),
                  ),
                ),
              );
            },
          );
        } else {
          return const Center(
            child: Text(
              "Erro ao carregar lojas",
              style: TextStyle(fontSize: 18, color: Colors.red),
            ),
          );
        }
      },
    );
  }
}
