import 'dart:convert';

import 'package:emartdriver/constants.dart';
import 'package:emartdriver/services/FirebaseHelper.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class VincularTaLiso extends StatefulWidget {
  const VincularTaLiso({super.key});

  @override
  State<VincularTaLiso> createState() => _VincularTaLisoState();
}

class _VincularTaLisoState extends State<VincularTaLiso> {
  Future<bool> cadastrarEntregador() async {
    final user = FirebaseAuth.instance.currentUser!;

    final usuario = await FireStoreUtils.getCurrentUser(user.uid);
    final payload = {
      "nome": "${usuario!.lastName} ${usuario.firstName}",
      "login": user.email,
      "senha": '',
      "email": usuario.email,
      "celular": usuario.phoneNumber,
      "endereco": '',
      "bairro": '',
      "cidade": '',
      "estado": '',
      "cep": '',
      "cpf": '',
      "data_nascimento": '',
      "data_cadastro": '',
      "vercode": '',
      "status": '',
      "token_fcm": '',
      "id_remoto": '',
      "tipo_veiculo": usuario.carName,
      "id_parceiro": '',
      "chave_pix": '',
      "cnh": '',
      "cnh_validade": '',
      "veiculo_placa": usuario.carNumber,
      "veiculo_descricao": usuario.carName,
      "foto": usuario.profilePictureURL,
    };

    final url = Uri.parse(
        "https://desenv.taentregue.site/mobile/v1/entregador/gerenciar/cadastro-entregador.php");
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(payload),
      );
      return jsonDecode(response.body)['success'];
    } catch (e) {
      debugPrint("Erro ao cadastrar entregador: \$e");
      return false;
    }
  }

  Future<Map<String, dynamic>> verificarVinculo(String idRemoto) async {
    final url = Uri.parse(
        "https://desenv.taentregue.site/mobile/v1/entregador/gerenciar/verificar-vinculo-entregador.php?id_remoto=\$idRemoto");
    try {
      final response = await http.get(url);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        return {"success": false, "message": "Erro: \${response.statusCode}"};
      }
    } catch (e) {
      debugPrint("Erro ao verificar vínculo: \$e");
      return {"success": false, "message": e.toString()};
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        padding: const EdgeInsets.all(16.0),
        child: FutureBuilder(
            future: verificarVinculo(FirebaseAuth.instance.currentUser!.uid),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (snapshot.hasError) {
                return const Center(
                  child: Text("Erro: \${snapshot.error}"),
                );
              }

              final data = snapshot.data;

              if (data == null || !data['success']) {
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      data != null ? data['message'] : "Erro desconhecido.",
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 16, color: Colors.red),
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            vertical: 12, horizontal: 24),
                        backgroundColor: Color(COLOR_PRIMARY),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onPressed: () async {
                        final success = await cadastrarEntregador();

                        if (success) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content:
                                  Text("Entregador cadastrado com sucesso!"),
                              backgroundColor: Colors.green,
                            ),
                          );
                          setState(() {}); // Rebuild to re-check vinculo
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text("Falha ao cadastrar entregador."),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      },
                      child: const Text(
                        'Vincular Ta Liso',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                );
              }

              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      "Bem-vindo(a), ${data['data']['nome']}!",
                      style: const TextStyle(fontSize: 18),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 10),
                    Text(
                      "Status: ${data['data']['status'] == 1 ? 'Ativo' : 'Inativo'}",
                      style: const TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  ],
                ),
              );
            }),
      ),
    );
  }
}
