import 'dart:io';

import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/User.dart';
import 'package:emartdriver/services/FirebaseHelper.dart';
import 'package:emartdriver/services/helper.dart';
import 'package:emartdriver/services/show_toast_dialog.dart';
import 'package:emartdriver/ui/documents/DocumentSubmittedScreen.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class DocumentsResubmitScreen extends StatefulWidget {
  final User user;
  const DocumentsResubmitScreen({Key? key, required this.user})
      : super(key: key);

  @override
  State<DocumentsResubmitScreen> createState() =>
      _DocumentsResubmitScreenState();
}

class _DocumentsResubmitScreenState extends State<DocumentsResubmitScreen> {
  File? _documentImage;
  File? _criminalRecordImage;
  File? _proofOfAddressImage;
  final ImagePicker _imagePicker = ImagePicker();

  DocumentStatus? documentStatus;
  DocumentStatus? criminalRecordStatus;
  DocumentStatus? proofOfAddressStatus;

  @override
  void initState() {
    super.initState();
    documentStatus = widget.user.document_accept;
    criminalRecordStatus = widget.user.criminalRecord_accept;
    proofOfAddressStatus = widget.user.proofOfAddress_accept;
  }

  Future<void> _pickImage(ImageSource source, String documentType) async {
    final XFile? image = await _imagePicker.pickImage(source: source);
    if (image != null) {
      setState(() {
        switch (documentType) {
          case 'document':
            _documentImage = File(image.path);
            break;
          case 'criminalRecord':
            _criminalRecordImage = File(image.path);
            break;
          case 'proofOfAddress':
            _proofOfAddressImage = File(image.path);
            break;
        }
      });
    }
  }

  Future<void> _uploadDocuments() async {
    if (_documentImage == null &&
        _criminalRecordImage == null &&
        _proofOfAddressImage == null) {
      ShowToastDialog.showToast('Selecione pelo menos um documento.');
      return;
    }
    try {
      final user = widget.user;
      if (_documentImage != null) {
        user.documentUrl = await FireStoreUtils.uploadCarImageToFireStorage(
            _documentImage!, user.userID, 'document');
      }
      if (_criminalRecordImage != null) {
        user.criminalRecordUrl =
            await FireStoreUtils.uploadCarImageToFireStorage(
                _criminalRecordImage!, user.userID, 'criminalRecord');
      }
      if (_proofOfAddressImage != null) {
        user.proofOfAddressUrl =
            await FireStoreUtils.uploadCarImageToFireStorage(
                _proofOfAddressImage!, user.userID, 'proofOfAddress');
      }
      user.document_accept = DocumentStatus.inAnalysis;
      user.criminalRecord_accept = DocumentStatus.inAnalysis;
      user.proofOfAddress_accept = DocumentStatus.inAnalysis;

      await FireStoreUtils.updateCurrentUser(user);
      ShowToastDialog.showToast('Documentos enviados com sucesso!');
      pushAndRemoveUntil(context, const DocumentSubmittedScreen(), false);
    } catch (e) {
      ShowToastDialog.showToast('Erro ao enviar documentos: $e');
    }
  }

  Widget _buildDocumentSection(
    String title,
    String description,
    File? imageFile,
    DocumentStatus? status,
    Function() onPickImage,
  ) {
    Color statusColor;
    String statusText;

    switch (status) {
      case DocumentStatus.inAnalysis:
        statusColor = Colors.orange;
        statusText = 'Em análise';
        break;
      case DocumentStatus.rejected:
        statusColor = Colors.red;
        statusText = 'Rejeitado';
        break;
      case DocumentStatus.approved:
        statusColor = Colors.green;
        statusText = 'Aprovado';
        break;
      default:
        statusColor = Colors.grey.shade400;
        statusText = 'Não enviado';
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Text(
                    statusText,
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Text(
              description,
              style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
            ),
            const SizedBox(height: 18),
            if (imageFile != null)
              Container(
                height: 200,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.grey.shade300),
                  image: DecorationImage(
                    image: FileImage(imageFile),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            const SizedBox(height: 18),
            status != DocumentStatus.approved
                ? ElevatedButton.icon(
                    onPressed: onPickImage,
                    icon: const Icon(Icons.upload_file),
                    label: Text(imageFile == null
                        ? 'Selecionar Arquivo'
                        : 'Alterar Arquivo'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(COLOR_PRIMARY),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      textStyle: const TextStyle(fontSize: 16),
                    ),
                  )
                : const SizedBox(),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reenvio de Documentos'),
        backgroundColor: Color(COLOR_PRIMARY),
        elevation: 1,
      ),
      backgroundColor: Colors.grey.shade100,
      body: SingleChildScrollView(
        padding: const EdgeInsets.only(bottom: 24),
        child: Column(
          children: [
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.amber.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.amber.shade200),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.amber.shade800),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'Importante',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Para garantir a aprovação rápida dos seus documentos:\n\n'
                    '• Envie fotos nítidas e bem iluminadas\n'
                    '• Certifique-se que todos os dados estejam legíveis\n'
                    '• Os documentos devem estar dentro do prazo de validade\n'
                    '• O comprovante de endereço deve ser recente',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
            _buildDocumentSection(
              'RG/CPF',
              'Envie uma foto do seu documento de identificação',
              _documentImage,
              documentStatus,
              () => _pickImage(ImageSource.gallery, 'document'),
            ),
            _buildDocumentSection(
              'Antecedentes Criminais',
              'Envie seu atestado de antecedentes criminais',
              _criminalRecordImage,
              criminalRecordStatus,
              () => _pickImage(ImageSource.gallery, 'criminalRecord'),
            ),
            _buildDocumentSection(
              'Comprovante de Endereço',
              'Envie um comprovante de residência em seu nome',
              _proofOfAddressImage,
              proofOfAddressStatus,
              () => _pickImage(ImageSource.gallery, 'proofOfAddress'),
            ),
            const SizedBox(height: 32),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ElevatedButton(
                onPressed: _uploadDocuments,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color(COLOR_PRIMARY),
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                ),
                child: const Text(
                  'Enviar Documentos',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 50),
          ],
        ),
      ),
    );
  }
}
