import 'dart:async';
import 'dart:io';

import 'package:audioplayers/audioplayers.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/User.dart';
import 'package:emartdriver/services/FirebaseHelper.dart';
import 'package:emartdriver/services/helper.dart';
import 'package:emartdriver/ui/auth/AuthScreen.dart';
import 'package:emartdriver/ui/bank_details/bank_details_Screen.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/ordersScreen/OrdersScreen.dart';
import 'package:emartdriver/ui/privacy_policy/privacy_policy.dart';
import 'package:emartdriver/ui/profile/ProfileScreen.dart';
import 'package:emartdriver/ui/termsAndCondition/terms_and_codition.dart';
import 'package:emartdriver/ui/vincular_lojas/vincular_lojas_lojas.dart';
import 'package:emartdriver/ui/vincular_ta_liso/vincular_ta_liso.dart';
import 'package:emartdriver/ui/wallet/WalletBalanceScreen.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:location/location.dart';

enum DrawerSelection {
  Home,
  vinculoLojas,
  vinculoTaLiso,
  Cuisines,
  Search,
  Cart,
  Drivers,
  rideSetting,
  Profile,
  Orders,
  Logout,
  Wallet, // Kept for compatibility with other screens
  BankInfo,
  termsCondition,
  privacyPolicy,
  inbox, // Kept for compatibility with other screens
  chooseLanguage, // Kept for compatibility with other screens
}

class ContainerScreen extends StatefulWidget {
  final User user;

  const ContainerScreen({
    Key? key,
    required this.user,
  }) : super(key: key);

  @override
  _ContainerScreen createState() {
    return _ContainerScreen();
  }
}

class _ContainerScreen extends State<ContainerScreen> {
  String _appBarTitle = 'Home'.tr();
  final fireStoreUtils = FireStoreUtils();
  late Widget _currentWidget;
  DrawerSelection _drawerSelection = DrawerSelection.Home;

  bool isLoading = true;
  @override
  void initState() {
    super.initState();
    _currentWidget = const HomeScreen();
    setCurrency();
    updateCurrentLocation();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      globalContext = context;
    });
    FireStoreUtils.firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
  }

  @override
  void dispose() {
    // Cancel the timer when the widget is disposed
    _statusUpdateTimer?.cancel();
    super.dispose();
  }

  setCurrency() async {
    /*FireStoreUtils().getCurrency().then((value) => value.forEach((element) {
          if (element.isactive = true) {
            currencyData = element;
          }
        }));*/
    await FireStoreUtils().getRazorPayDemo();
    await FireStoreUtils.getPaypalSettingData();
    await FireStoreUtils.getPayStackSettingData();
    await FireStoreUtils.getFlutterWaveSettingData();
    await FireStoreUtils.getPaytmSettingData();
    await FireStoreUtils.getWalletSettingData();
    await FireStoreUtils.getPayFastSettingData();
    await FireStoreUtils.getMercadoPagoSettingData();
    await FireStoreUtils.getDriverOrderSetting();
    await FireStoreUtils.getOrangeMoneySettingData();
    await FireStoreUtils.getXenditSettingData();
    await FireStoreUtils.getMidTransSettingData();

    setState(() {
      isLoading = false;
    });
  }

  Location location = Location();

  updateCurrentLocation() async {
    PermissionStatus permissionStatus = await location.hasPermission();

    if (permissionStatus == PermissionStatus.granted) {
      print("---->");
      location.enableBackgroundMode(enable: true);
      location.changeSettings(
          accuracy: LocationAccuracy.navigation, distanceFilter: 50);
      location.onLocationChanged.listen((locationData) async {
        locationDataFinal = locationData;

        await FireStoreUtils.getCurrentUser(MyAppState.currentUser!.userID)
            .then((value) {
          if (value != null) {
            User driverUserModel = value;
            if (driverUserModel.isActive == true) {
              driverUserModel.location = UserLocation(
                  latitude: locationData.latitude ?? 0.0,
                  longitude: locationData.longitude ?? 0.0);
              driverUserModel.rotation = locationData.heading;
              FireStoreUtils.updateCurrentUser(driverUserModel);
            }
          }
        });
      });
    } else {
      await openBackgroundLocationDialog();
      await location.requestPermission().then((permissionStatus) {
        if (permissionStatus == PermissionStatus.granted) {
          location.enableBackgroundMode(enable: true);
          location.changeSettings(
              accuracy: LocationAccuracy.navigation, distanceFilter: 50);
          location.onLocationChanged.listen((locationData) async {
            locationDataFinal = locationData;
            await FireStoreUtils.getCurrentUser(MyAppState.currentUser!.userID)
                .then((value) {
              if (value != null) {
                User driverUserModel = value;
                if (driverUserModel.isActive == true) {
                  driverUserModel.location = UserLocation(
                      latitude: locationData.latitude ?? 0.0,
                      longitude: locationData.longitude ?? 0.0);
                  driverUserModel.rotation = locationData.heading;
                  FireStoreUtils.updateCurrentUser(driverUserModel);
                }
              }
            });
          });
        }
      });
    }
  }

  openBackgroundLocationDialog() {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16.0))),
            contentPadding: const EdgeInsets.only(top: 10.0),
            content: SizedBox(
              //width: 300.0,
              width: MediaQuery.of(context).size.width * 0.6,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Padding(
                    padding:
                        const EdgeInsets.only(left: 8.0, right: 8.0, top: 8.0),
                    child: Text(
                      "Background Location permission".tr(),
                      style: const TextStyle(
                          color: Colors.black, fontWeight: FontWeight.bold),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 8.0, right: 8.0, top: 8.0, bottom: 8.0),
                    child: Text(
                        "This app collects location data to enable location fetching at the time of you are on the way to deliver order or even when the app is in background."
                            .tr()),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      padding: const EdgeInsets.only(top: 20.0, bottom: 20.0),
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(16.0),
                            bottomRight: Radius.circular(16.0)),
                      ),
                      child: Text(
                        "Okay".tr(),
                        style: const TextStyle(color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }

  DateTime pre_backpress = DateTime.now();

  final audioPlayer = AudioPlayer(playerId: "playerId");
  bool isOnline = false;
  Timer? _statusUpdateTimer;

  // Function to update the delivery_men_status collection
  void _updateDeliveryMenStatus() {
    if (isOnline && MyAppState.currentUser != null) {
      FirebaseFirestore.instance
          .collection('delivery_men_status')
          .doc(MyAppState.currentUser!.userID)
          .set({
        'entregador_id': MyAppState.currentUser!.userID,
        'lastActive': Timestamp.now(),
        'user_id': auth.FirebaseAuth.instance.currentUser?.uid ?? '',
      }, SetOptions(merge: true)).then((_) {
        print('Updated delivery_men_status timestamp');
      }).catchError((error) {
        print('Error updating delivery_men_status: $error');
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        final timegap = DateTime.now().difference(pre_backpress);
        final cantExit = timegap >= const Duration(seconds: 2);
        pre_backpress = DateTime.now();
        if (cantExit) {
          //show snackbar
          final snack = SnackBar(
            content: Text(
              'Press Back button again to Exit'.tr(),
              style: const TextStyle(color: Colors.white),
            ),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.black,
          );
          ScaffoldMessenger.of(context).showSnackBar(snack);
          return false; // false will do nothing when back press
        } else {
          return true; // true will exit the app
        }
      },
      child: Scaffold(
        drawer: Drawer(
          child: Column(
            children: [
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    DrawerHeader(
                      margin: const EdgeInsets.all(0.0),
                      padding: const EdgeInsets.all(10),
                      decoration: const BoxDecoration(
                        color: Color(0xff425799),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          displayCircleImage(
                              MyAppState.currentUser!.profilePictureURL,
                              60,
                              false),
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              MyAppState.currentUser!.fullName(),
                              style: const TextStyle(color: Colors.white),
                            ),
                          ),
                          Text(
                            MyAppState.currentUser!.email,
                            style: const TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                    ListTileTheme(
                      style: ListTileStyle.drawer,
                      selectedColor: const Color(0xff425799),
                      child: ListTile(
                        selected: _drawerSelection == DrawerSelection.Home,
                        title: const Text('Home').tr(),
                        onTap: () {
                          Navigator.pop(context);
                          setState(() {
                            _drawerSelection = DrawerSelection.Home;
                            _appBarTitle = 'Home'.tr();
                            // Create a new instance of HomeScreen to ensure it's properly initialized
                            _currentWidget = const HomeScreen();
                          });

                          // Add a small delay to ensure the widget is built before trying to access it
                          Future.delayed(const Duration(milliseconds: 500), () {
                            // Force a rebuild of the map if needed
                            if (_currentWidget is HomeScreen) {
                              // This will trigger a rebuild of the map
                              setState(() {});
                            }
                          });
                        },
                        leading: const Icon(CupertinoIcons.home),
                      ),
                    ),
                    ListTileTheme(
                      style: ListTileStyle.drawer,
                      selectedColor: const Color(0xff425799),
                      child: ListTile(
                        selected:
                            _drawerSelection == DrawerSelection.vinculoLojas,
                        title: const Text('Vincular lojas').tr(),
                        onTap: () {
                          Navigator.pop(context);
                          setState(() {
                            _drawerSelection = DrawerSelection.vinculoLojas;
                            _appBarTitle = 'Vincular lojas';
                            _currentWidget = const VincularLojasPage();
                          });
                        },
                        leading: const Icon(CupertinoIcons.resize_h),
                      ),
                    ),
                    ListTileTheme(
                      style: ListTileStyle.drawer,
                      selectedColor: const Color(0xff425799),
                      child: ListTile(
                        selected:
                            _drawerSelection == DrawerSelection.vinculoTaLiso,
                        title: const Text('Vincular ao ta liso').tr(),
                        onTap: () {
                          Navigator.pop(context);
                          setState(
                            () {
                              _drawerSelection = DrawerSelection.vinculoTaLiso;
                              _appBarTitle = 'Vincular ao ta liso';
                              _currentWidget = const VincularTaLiso();
                            },
                          );
                        },
                        leading: const Icon(CupertinoIcons.resize_v),
                      ),
                    ),
                    ListTileTheme(
                      style: ListTileStyle.drawer,
                      selectedColor: const Color(0xff425799),
                      child: ListTile(
                        selected: _drawerSelection == DrawerSelection.Orders,
                        leading: Image.asset(
                          'assets/images/truck.png',
                          color: _drawerSelection == DrawerSelection.Orders
                              ? const Color(0xff425799)
                              : isDarkMode(context)
                                  ? Colors.grey.shade200
                                  : Colors.grey.shade600,
                          width: 24,
                          height: 24,
                        ),
                        title: const Text('Orders').tr(),
                        onTap: () {
                          Navigator.pop(context);
                          setState(() {
                            _drawerSelection = DrawerSelection.Orders;
                            _appBarTitle = 'Orders'.tr();
                            _currentWidget = const OrdersScreen();
                          });
                        },
                      ),
                    ),
                    ListTileTheme(
                      style: ListTileStyle.drawer,
                      selectedColor: const Color(0xff425799),
                      child: ListTile(
                        selected: _drawerSelection == DrawerSelection.Wallet,
                        leading: const Icon(Icons.account_balance_wallet),
                        title: const Text('Carteira').tr(),
                        onTap: () {
                          Navigator.pop(context);
                          setState(() {
                            _drawerSelection = DrawerSelection.Wallet;
                            _appBarTitle = 'Carteira'.tr();
                            _currentWidget = const WalletBalanceScreen();
                          });
                        },
                      ),
                    ),
                    ListTileTheme(
                      style: ListTileStyle.drawer,
                      selectedColor: const Color(0xff425799),
                      child: ListTile(
                        selected: _drawerSelection == DrawerSelection.BankInfo,
                        leading: const Icon(Icons.account_balance),
                        title: const Text('Withdraw method').tr(),
                        onTap: () {
                          Navigator.pop(context);
                          setState(() {
                            _drawerSelection = DrawerSelection.BankInfo;
                            _appBarTitle = 'Withdraw method'.tr();
                            _currentWidget = const BankDetailsScreen();
                          });
                        },
                      ),
                    ),
                    ListTileTheme(
                      style: ListTileStyle.drawer,
                      selectedColor: const Color(0xff425799),
                      child: ListTile(
                        selected: _drawerSelection == DrawerSelection.Profile,
                        leading: const Icon(CupertinoIcons.person),
                        title: const Text('Profile').tr(),
                        onTap: () {
                          Navigator.pop(context);
                          setState(() {
                            _drawerSelection = DrawerSelection.Profile;
                            _appBarTitle = 'My Profile'.tr();
                            _currentWidget = ProfileScreen(
                              user: MyAppState.currentUser!,
                            );
                          });
                        },
                      ),
                    ),
                    ListTileTheme(
                      style: ListTileStyle.drawer,
                      selectedColor: const Color(0xff425799),
                      child: ListTile(
                        selected:
                            _drawerSelection == DrawerSelection.termsCondition,
                        leading: const Icon(Icons.policy),
                        title: const Text('Terms and Condition').tr(),
                        onTap: () async {
                          push(context, const TermsAndCondition());
                        },
                      ),
                    ),
                    ListTileTheme(
                      style: ListTileStyle.drawer,
                      selectedColor: const Color(0xff425799),
                      child: ListTile(
                        selected:
                            _drawerSelection == DrawerSelection.privacyPolicy,
                        leading: const Icon(Icons.privacy_tip),
                        title: const Text('Privacy policy').tr(),
                        onTap: () async {
                          push(context, const PrivacyPolicyScreen());
                        },
                      ),
                    ),
                    ListTileTheme(
                      style: ListTileStyle.drawer,
                      selectedColor: const Color(0xff425799),
                      child: ListTile(
                        selected: _drawerSelection == DrawerSelection.Logout,
                        leading: const Icon(Icons.logout),
                        title: const Text('Log out').tr(),
                        onTap: () async {
                          audioPlayer.stop();
                          Navigator.pop(context);
                          await FireStoreUtils.getCurrentUser(
                                  MyAppState.currentUser!.userID)
                              .then((value) {
                            MyAppState.currentUser = value;
                          });
                          MyAppState.currentUser!.isActive = false;
                          MyAppState.currentUser!.lastOnlineTimestamp =
                              Timestamp.now();
                          await FireStoreUtils.updateCurrentUser(
                              MyAppState.currentUser!);
                          await auth.FirebaseAuth.instance.signOut();
                          MyAppState.currentUser = null;
                          location.enableBackgroundMode(enable: false);
                          pushAndRemoveUntil(context, AuthScreen(), false);
                        },
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text("V : $appVersion"),
              )
            ],
          ),
        ),
        appBar: AppBar(
          iconTheme: IconThemeData(
            color: isDarkMode(context) ? Colors.white : const Color(DARK_COLOR),
          ),
          centerTitle: false,
          backgroundColor:
              isDarkMode(context) ? const Color(DARK_COLOR) : Colors.white,
          actions: [
            Container(
              margin: const EdgeInsets.only(right: 10),
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                color: const Color(0xff425799),
              ),
              child: Image.asset(
                'assets/images/capacete.png',
                // color: Color(COLOR_PRIMARY),
                fit: BoxFit.cover,
                width: 20,
                height: 20,
              ),
            ),
            Text(
              "você está ${isOnline ? "online" : "offline"}",
              style: const TextStyle(
                color: Color(0xff425799),
                fontSize: 16,
              ),
            ),
            const SizedBox(width: 20),
            Switch(
              value: isOnline,
              activeColor: const Color(0xff425799),
              onChanged: (bool value) {
                if (isOnline && !value) {
                  // Usuário está mudando de online para offline - mostrar diálogo
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: const Text('Atenção'),
                        content: const Text(
                            'Você está ficando offline. Deseja fechar o aplicativo?'),
                        actions: [
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).pop(); // Fecha o diálogo
                              setState(() {
                                isOnline = false;
                              });
                              // Cancela o timer
                              _statusUpdateTimer?.cancel();
                              _statusUpdateTimer = null;
                            },
                            child: const Text('Não, continuar no app'),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).pop(); // Fecha o diálogo
                              // Stop the timer
                              _statusUpdateTimer?.cancel();
                              // Fechar o aplicativo
                              exit(0);
                            },
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.red,
                            ),
                            child: const Text('Sim, fechar app'),
                          ),
                        ],
                      );
                    },
                  );
                } else {
                  // Usuário está mudando para online ou interagindo pela primeira vez
                  setState(() {
                    isOnline = value;
                  });

                  // Start or stop the timer based on online status
                  if (value) {
                    // Start the timer and immediately update status
                    _updateDeliveryMenStatus();
                    _statusUpdateTimer =
                        Timer.periodic(const Duration(minutes: 1), (timer) {
                      _updateDeliveryMenStatus();
                    });
                  } else {
                    // Stop the timer
                    _statusUpdateTimer?.cancel();
                    _statusUpdateTimer = null;
                  }
                }
              },
            ),
          ],
        ),
        body: isLoading
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : _currentWidget,
      ),
    );
  }
}
