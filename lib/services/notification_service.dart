import 'dart:convert';
import 'dart:developer';
import 'dart:io' show Platform;

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

Future<void> firebaseMessageBackgroundHandle(RemoteMessage message) async {
  log("BackGround Message :: ${message.messageId}");
}

class NotificationService {
  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  initInfo() async {
    try {
      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      var request = await FirebaseMessaging.instance.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (request.authorizationStatus == AuthorizationStatus.authorized ||
          request.authorizationStatus == AuthorizationStatus.provisional) {
        const AndroidInitializationSettings initializationSettingsAndroid =
            AndroidInitializationSettings('@mipmap/ic_launcher');
        var iosInitializationSettings = const DarwinInitializationSettings();
        final InitializationSettings initializationSettings =
            InitializationSettings(
                android: initializationSettingsAndroid,
                iOS: iosInitializationSettings);
        await flutterLocalNotificationsPlugin.initialize(initializationSettings,
            onDidReceiveNotificationResponse: (payload) {});
        await setupInteractedMessage();
      }
    } catch (e) {
      log("Error initializing notifications: $e");
    }
  }

  static Future<String?> getToken() async {
    try {
      if (Platform.isIOS) {
        // Get APNS token first for iOS
        String? apnsToken = await FirebaseMessaging.instance.getAPNSToken();
        if (apnsToken == null) {
          // Wait for a moment and try again with multiple attempts
          for (int i = 0; i < 3; i++) {
            await Future.delayed(const Duration(seconds: 2));
            apnsToken = await FirebaseMessaging.instance.getAPNSToken();
            if (apnsToken != null) break;
          }
        }

        if (apnsToken != null) {
          // Now get FCM token
          return await FirebaseMessaging.instance.getToken();
        }

        // If APNS token is still null, we'll return a temporary token for now
        // This prevents login failures, and the token will be updated later
        log("APNS token not available yet");
        return "temporary_token_for_ios";
      } else {
        // For Android, directly get FCM token
        return await FirebaseMessaging.instance.getToken();
      }
    } catch (e) {
      log("Error getting token: $e");
      // Return a temporary token to prevent login failures
      return "temporary_token_on_error";
    }
  }

  Future<void> setupInteractedMessage() async {
    try {
      RemoteMessage? initialMessage =
          await FirebaseMessaging.instance.getInitialMessage();
      if (initialMessage != null) {
        FirebaseMessaging.onBackgroundMessage(
            (message) => firebaseMessageBackgroundHandle(message));
      }

      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        log("::::::::::::onMessage:::::::::::::::::");
        if (message.notification != null) {
          log(message.notification.toString());
          display(message);
        }
      });

      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        log("::::::::::::onMessageOpenedApp:::::::::::::::::");
        if (message.notification != null) {
          handleNotificationTap(message);
        }
      });

      // Only subscribe to topic after ensuring we have a valid token
      if (Platform.isIOS) {
        String? apnsToken = await FirebaseMessaging.instance.getAPNSToken();
        if (apnsToken != null) {
          await FirebaseMessaging.instance.subscribeToTopic("eMart_driver");
        }
      } else {
        await FirebaseMessaging.instance.subscribeToTopic("eMart_driver");
      }

      log("::::::::::::Permission authorized:::::::::::::::::");
    } catch (e) {
      log("Error setting up messages: $e");
    }
  }

  void handleNotificationTap(RemoteMessage message) {}

  void display(RemoteMessage message) async {
    log('Got a message whilst in the foreground!');
    log('Message data: ${message.notification!.body.toString()}');
    try {
      // final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      AndroidNotificationChannel channel = const AndroidNotificationChannel(
        "01",
        "emart_driver",
        description: 'Show Ta entregue Notification',
        importance: Importance.max,
      );
      AndroidNotificationDetails notificationDetails =
          AndroidNotificationDetails(channel.id, channel.name,
              channelDescription: 'your channel Description',
              importance: Importance.high,
              priority: Priority.high,
              ticker: 'ticker');
      const DarwinNotificationDetails darwinNotificationDetails =
          DarwinNotificationDetails(
              presentAlert: true, presentBadge: true, presentSound: true);
      NotificationDetails notificationDetailsBoth = NotificationDetails(
          android: notificationDetails, iOS: darwinNotificationDetails);
      await FlutterLocalNotificationsPlugin().show(
        0,
        message.notification!.title,
        message.notification!.body,
        notificationDetailsBoth,
        payload: jsonEncode(message.data),
      );
    } on Exception catch (e) {
      log(e.toString());
    }
  }
}
