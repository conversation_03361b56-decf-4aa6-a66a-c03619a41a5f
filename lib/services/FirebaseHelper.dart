import 'dart:async';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/BlockUserModel.dart';
import 'package:emartdriver/model/CabOrderModel.dart';
import 'package:emartdriver/model/CarMakes.dart';
import 'package:emartdriver/model/CurrencyModel.dart';
import 'package:emartdriver/model/DeliveryChargeModel.dart';
import 'package:emartdriver/model/FlutterWaveSettingDataModel.dart';
import 'package:emartdriver/model/MercadoPagoSettingsModel.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/model/PayFastSettingData.dart';
import 'package:emartdriver/model/PayStackSettingsModel.dart';
import 'package:emartdriver/model/Ratingmodel.dart';
import 'package:emartdriver/model/SectionModel.dart';
import 'package:emartdriver/model/User.dart';
import 'package:emartdriver/model/VehicleType.dart';
import 'package:emartdriver/model/VendorModel.dart';
import 'package:emartdriver/model/conversation_model.dart';
import 'package:emartdriver/model/email_template_model.dart';
import 'package:emartdriver/model/inbox_model.dart';
import 'package:emartdriver/model/notification_model.dart';
import 'package:emartdriver/model/payment_model/mid_trans.dart';
import 'package:emartdriver/model/payment_model/orange_money.dart';
import 'package:emartdriver/model/payment_model/xendit.dart';
import 'package:emartdriver/model/paypalSettingData.dart';
import 'package:emartdriver/model/paytmSettingData.dart';
import 'package:emartdriver/model/razorpayKeyModel.dart';
import 'package:emartdriver/model/referral_model.dart';
import 'package:emartdriver/model/withdrawHistoryModel.dart';
import 'package:emartdriver/model/withdraw_method_model.dart';
import 'package:emartdriver/ui/reauthScreen/reauth_user_screen.dart';
import 'package:emartdriver/userPrefrence.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:the_apple_sign_in/the_apple_sign_in.dart' as apple;
import 'package:uuid/uuid.dart';
import 'package:video_compress/video_compress.dart';

class FireStoreUtils {
  static FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
  static FirebaseFirestore firestore = FirebaseFirestore.instance;
  static Reference storage = FirebaseStorage.instance.ref();
  List<BlockUserModel> blockedList = [];

  /// Helper method to safely get FCM token, handling APNS token not available error
  static Future<String> _getSafeFCMToken() async {
    try {
      // For iOS, check if APNS token is available
      if (Platform.isIOS) {
        // Get the APNS token first
        String? apnsToken = await firebaseMessaging.getAPNSToken();
        if (apnsToken == null) {
          // If APNS token is not available, use a temporary token for iOS
          print('APNS token not available yet');
          return 'temporary_token_for_ios';
        }
      }

      // Try to get FCM token
      return await firebaseMessaging.getToken() ?? '';
    } catch (e) {
      print('Failed to get FCM token: $e');
      return '';
    }
  }

  static Future addDriverInbox(InboxModel inboxModel) async {
    return await firestore
        .collection("chat_driver")
        .doc(inboxModel.orderId)
        .set(inboxModel.toJson())
        .then((document) {
      return inboxModel;
    });
  }

  static Future addDriverChat(ConversationModel conversationModel) async {
    return await firestore
        .collection("chat_driver")
        .doc(conversationModel.orderId)
        .collection("thread")
        .doc(conversationModel.id)
        .set(conversationModel.toJson())
        .then((document) {
      return conversationModel;
    });
  }

  Future<List<RatingModel>> getReviewByDriverId(String driverId) async {
    List<RatingModel> vendorreview = [];

    QuerySnapshot<Map<String, dynamic>> vendorsQuery = await firestore
        .collection(Order_Rating)
        .where('driverId', isEqualTo: driverId)
        // .orderBy('createdAt', descending: true)
        .get();
    await Future.forEach(vendorsQuery.docs,
        (QueryDocumentSnapshot<Map<String, dynamic>> document) {
      print(document);
      try {
        vendorreview.add(RatingModel.fromJson(document.data()));
      } catch (e) {
        print('FireStoreUtils.getOrders Parse error ${document.id} $e');
      }
    });
    return vendorreview;
  }

  static Future getDriverOrderSetting() async {
    DocumentSnapshot<Map<String, dynamic>> codQuery =
        await firestore.collection(Setting).doc('DriverNearBy').get();
    if (codQuery.data() != null) {
      minimumDepositToRideAccept = codQuery['minimumDepositToRideAccept'];
      minimumAmountToWithdrawal = codQuery['minimumAmountToWithdrawal'];
      driverOrderAcceptRejectDuration =
          int.parse(codQuery["driverOrderAcceptRejectDuration"].toString());
      enableOTPParcelReceive = codQuery["enableOTPParcelReceive"] ?? false;
      enableOTPTripStart = codQuery["enableOTPTripStart"] ?? false;
    } else {
      return "";
    }
    return null;
  }

  static Future<WithdrawMethodModel?> getWithdrawMethod() async {
    WithdrawMethodModel? withdrawMethodModel;
    await firestore
        .collection(withdrawMethod)
        .where("userId", isEqualTo: MyAppState.currentUser!.userID)
        .get()
        .then((value) async {
      if (value.docs.isNotEmpty) {
        withdrawMethodModel =
            WithdrawMethodModel.fromJson(value.docs.first.data());
      }
    });
    return withdrawMethodModel;
  }

  static Future<WithdrawMethodModel?> setWithdrawMethod(
      WithdrawMethodModel withdrawMethodModel) async {
    if (withdrawMethodModel.id == null) {
      withdrawMethodModel.id = const Uuid().v4();
      withdrawMethodModel.userId = MyAppState.currentUser!.userID;
    }
    await firestore
        .collection(withdrawMethod)
        .doc(withdrawMethodModel.id)
        .set(withdrawMethodModel.toJson())
        .then((value) async {});
    return withdrawMethodModel;
  }

  Future<List<CabOrderModel>> getCabOrderByDriverOrder(
      String userID, String companyId) async {
    List<CabOrderModel> orders = [];
    QuerySnapshot<Map<String, dynamic>> ordersQuery;
    ordersQuery = await firestore
        .collection(RIDESORDER)
        .where('driverID', isEqualTo: userID)
        .orderBy('createdAt', descending: true)
        .get();

    await Future.forEach(ordersQuery.docs,
        (QueryDocumentSnapshot<Map<String, dynamic>> document) {
      try {
        orders.add(CabOrderModel.fromJson(document.data()));
      } catch (e, stacksTrace) {
        print('FireStoreUtils.getDriverOrders Parse error ${document.id} $e '
            '$stacksTrace');
      }
    });
    return orders;
  }

  late StreamController<User> driverStreamController;
  late StreamSubscription driverStreamSub;

  Stream<User> getDriver(String userId) async* {
    driverStreamController = StreamController();
    driverStreamSub = firestore
        .collection(USERS)
        .doc(userId)
        .snapshots()
        .listen((onData) async {
      if (onData.data() != null) {
        User? user = User.fromJson(onData.data()!);
        driverStreamController.sink.add(user);
      }
    });
    yield* driverStreamController.stream;
  }

  static Future<List<VehicleType>> getVehicleType(
      SectionModel? sectionModel) async {
    print("----------->");
    print(sectionModel!.id);
    List<VehicleType> vehicleType = [];
    QuerySnapshot<Map<String, dynamic>> currencyQuery = await firestore
        .collection(VEHICLETYPE)
        .where('sectionId', isEqualTo: sectionModel.id)
        .where("isActive", isEqualTo: true)
        .get();
    await Future.forEach(currencyQuery.docs,
        (QueryDocumentSnapshot<Map<String, dynamic>> document) {
      try {
        vehicleType.add(VehicleType.fromJson(document.data()));
      } catch (e) {
        print('FireStoreUtils.getCurrencys Parse error $e');
      }
    });
    return vehicleType;
  }

  static Future<List<SectionModel>> getSections() async {
    List<SectionModel> sections = [];
    QuerySnapshot<Map<String, dynamic>> productsQuery = await firestore
        .collection(SECTION)
        .where("isActive", isEqualTo: true)
        .where('serviceTypeFlag', isEqualTo: "cab-service")
        .get();
    await Future.forEach(productsQuery.docs,
        (QueryDocumentSnapshot<Map<String, dynamic>> document) {
      try {
        if (document.data()['name'] != "Banner") {
          sections.add(SectionModel.fromJson(document.data()));
        }
      } catch (e) {
        print('**-FireStoreUtils.getSection Parse error $e');
      }
    });

    return sections;
  }

  static Future<List<VehicleType>> getRentalVehicleType() async {
    List<VehicleType> vehicleType = [];
    QuerySnapshot<Map<String, dynamic>> currencyQuery = await firestore
        .collection(RENTALVEHICLETYPE)
        .where("isActive", isEqualTo: true)
        .get();
    await Future.forEach(currencyQuery.docs,
        (QueryDocumentSnapshot<Map<String, dynamic>> document) {
      try {
        print(document.data());
        vehicleType.add(VehicleType.fromJson(document.data()));
      } catch (e) {
        print('FireStoreUtils.getCurrencys Parse error $e');
      }
    });
    return vehicleType;
  }

  static Future<List<CarMakes>> getCarMakes() async {
    List<CarMakes> carMakesList = [];
    QuerySnapshot<Map<String, dynamic>> currencyQuery = await firestore
        .collection(CARMAKES)
        .where("isActive", isEqualTo: true)
        .get();
    await Future.forEach(currencyQuery.docs,
        (QueryDocumentSnapshot<Map<String, dynamic>> document) {
      try {
        carMakesList.add(CarMakes.fromJson(document.data()));
      } catch (e) {
        print('FireStoreUtils.getCurrencys Parse error $e');
      }
    });
    return carMakesList;
  }

  static Future<User?> getCurrentUser(String uid) async {
    DocumentSnapshot<Map<String, dynamic>> userDocument =
        await firestore.collection(USERS).doc(uid).get();
    if (userDocument.data() != null && userDocument.exists) {
      // print('milaa');

      return User.fromJson(userDocument.data()!);
    } else {
      return null;
    }
  }

  static Future<CabOrderModel?> getCabOrderByOrderId(String orderID) async {
    DocumentSnapshot<Map<String, dynamic>> userDocument =
        await firestore.collection(RIDESORDER).doc(orderID).get();
    if (userDocument.data() != null && userDocument.exists) {
      return CabOrderModel.fromJson(userDocument.data()!);
    } else {
      return null;
    }
  }

  /*Future<List<CurrencyModel>> getCurrency() async {
    List<CurrencyModel> currency = [];

    QuerySnapshot<Map<String, dynamic>> currencyQuery = await firestore.collection(Currency).where('isActive', isEqualTo: true).get();
    await Future.forEach(currencyQuery.docs, (QueryDocumentSnapshot<Map<String, dynamic>> document) {
      try {
        currency.add(CurrencyModel.fromJson(document.data()));
      } catch (e) {
        print('FireStoreUtils.getCurrencys Parse error $e');
      }
    });
    return currency;
  }*/
  Future<CurrencyModel?> getCurrency() async {
    CurrencyModel? currency;
    await firestore
        .collection(Currency)
        .where("isActive", isEqualTo: true)
        .get()
        .then((value) {
      if (value.docs.isNotEmpty) {
        currency = CurrencyModel.fromJson(value.docs.first.data());
      }
    });
    return currency;
  }

  Future<VendorModel> getVendorByVendorID(String vendorID) async {
    late VendorModel vendor;
    print("$vendorID----VENDORIDPLACEORDER");
    QuerySnapshot<Map<String, dynamic>> vendorsQuery = await firestore
        .collection(VENDORS)
        .where('id', isEqualTo: vendorID)
        .get();
    try {
      if (vendorsQuery.docs.isNotEmpty) {
        vendor = VendorModel.fromJson(vendorsQuery.docs.first.data());
      }
    } catch (e) {
      print('FireStoreUtils.getVendorByVendorID Parse error $e');
    }
    return vendor;
  }

  Future<DeliveryChargeModel?> getDeliveryCharges() async {
    DocumentSnapshot<Map<String, dynamic>> codQuery =
        await firestore.collection(Setting).doc('DeliveryCharge').get();
    if (codQuery.data() != null) {
      return DeliveryChargeModel.fromJson(codQuery.data()!);
    } else {
      return null;
    }
  }

  static Future<User?> updateCurrentUser(User user) async {
    return await firestore
        .collection(USERS)
        .doc(user.userID)
        .set(user.toJson())
        .then((document) {
      return user;
    });
  }

  static Future saveDriverAdditionalInfo(
      String userId, Map<String, dynamic> additionalData) async {
    return await firestore
        .collection('driver_additional_info')
        .doc(userId)
        .set(additionalData);
  }

  getContactUs() async {
    Map<String, dynamic> contactData = {};
    await firestore.collection(Setting).doc(CONTACT_US).get().then((value) {
      contactData = value.data()!;
    });

    return contactData;
  }

  getplaceholderimage() async {
    var collection = FirebaseFirestore.instance.collection(Setting);
    var docSnapshot = await collection.doc('placeHolderImage').get();

    Map<String, dynamic>? data = docSnapshot.data();
    var value = data?['image'];
    placeholderImage = value;
    return const Center();
  }

  static Future orderTransaction(
      {required OrderModel orderModel,
      required num amount,
      required num driveramount}) async {
    DocumentReference documentReference =
        firestore.collection(OrderTransaction).doc();
    Map<String, dynamic> data = {
      "order_id": orderModel.id,
      "id": documentReference.id,
      "date": DateTime.now(),
    };
    print("Error is false called transaction");
    if (orderModel.takeAway!) {
      data.addAll({"vendorId": orderModel.vendorID, "vendorAmount": amount});
    } else {
      data.addAll({
        "vendorId": orderModel.vendorID,
        "vendorAmount": amount,
        "driverId": orderModel.driverID,
        "driverAmount": driveramount
      });
    }

    await firestore
        .collection(OrderTransaction)
        .doc(documentReference.id)
        .set(data)
        .then((value) {});
    return "updated transaction".tr();
  }

  static Future cabOrderTransaction(
      {required CabOrderModel orderModel, required num driveramount}) async {
    DocumentReference documentReference =
        firestore.collection(OrderTransaction).doc();
    Map<String, dynamic> data = {
      "order_id": orderModel.id,
      "id": documentReference.id,
      "date": DateTime.now(),
    };
    print("Error is false called transaction");
    data.addAll({
      "vendorId": "",
      "vendorAmount": "",
      "driverId": orderModel.driverID,
      "driverAmount": driveramount
    });

    await firestore
        .collection(OrderTransaction)
        .doc(documentReference.id)
        .set(data)
        .then((value) {});
    return "updated transaction".tr();
  }

  static Future createPaymentId({collectionName = "wallet"}) async {
    DocumentReference documentReference =
        firestore.collection(collectionName).doc();
    final paymentId = documentReference.id;
    //UserPreference.setPaymentId(paymentId: paymentId);
    return paymentId;
  }

  static Future topUpWalletAmount(
      {String serviceType = "",
      String paymentMethod = "test",
      bool isTopup = true,
      required amount,
      required id,
      orderId = "",
      required String userID}) async {
    print("this is te payment id");
    print(id);

    await firestore.collection(Wallet).doc(id).set({
      "serviceType": serviceType,
      "user_id": userID,
      "payment_method": paymentMethod,
      "amount": amount,
      "id": id,
      "order_id": orderId,
      "isTopUp": isTopup,
      "payment_status": "success",
      "date": DateTime.now(),
    }).then((value) {
      firestore.collection(Wallet).doc(id).get().then((value) {
        DocumentSnapshot<Map<String, dynamic>> documentData = value;
        print("nato");
        print(documentData.data());
      });
    });

    return "updated Amount".tr();
  }

  static Future updateWalletAmount(
      {required String userId, required amount}) async {
    dynamic walletAmount = 0;
    await firestore.collection(USERS).doc(userId).get().then((value) async {
      DocumentSnapshot<Map<String, dynamic>> userDocument = value;
      if (userDocument.data() != null && userDocument.exists) {
        try {
          print(userDocument.data());
          await firestore.collection(USERS).doc(userId).update({
            "wallet_amount": userDocument.data()!['wallet_amount'] + amount
          }).then((value) => print("north"));

          DocumentSnapshot<Map<String, dynamic>> newUserDocument =
              await firestore.collection(USERS).doc(userId).get();
          MyAppState.currentUser = User.fromJson(newUserDocument.data()!);
          print(MyAppState.currentUser);
        } catch (error) {
          print(error);
          if (error.toString() ==
              "Bad state: field does not exist within the DocumentSnapshotPlatform") {
            print("does not exist");
          } else {
            print("went wrong!!");
            walletAmount = "ERROR";
          }
        }
        print("data val");
        print(walletAmount);
        return walletAmount; //User.fromJson(userDocument.data()!);
      } else {
        return 0.111;
      }
    });
  }

  static Future updateUserWalletAmount(
      {required String userId, required amount}) async {
    await firestore.collection(USERS).doc(userId).get().then((value) async {
      DocumentSnapshot<Map<String, dynamic>> userDocument = value;
      if (userDocument.data() != null && userDocument.exists) {
        try {
          print("--->amount---- $amount");
          print(userDocument.data());
          User user = User.fromJson(userDocument.data()!);
          await firestore
              .collection(USERS)
              .doc(userId)
              .update({"wallet_amount": user.walletAmount + amount}).then(
                  (value) => print("north"));
        } catch (error) {
          print(error);
          if (error.toString() ==
              "Bad state: field does not exist within the DocumentSnapshotPlatform") {
            print("does not exist");
          } else {
            print("went wrong!!");
          }
        }
        print("data val");
        return ""; //User.fromJson(userDocument.data()!);
      } else {
        return 0.111;
      }
    });
  }

  static Future withdrawWalletAmount(
      {required WithdrawHistoryModel withdrawHistory}) async {
    print("this is te payment id");
    print(withdrawHistory.id);
    print(MyAppState.currentUser!.userID);

    await firestore
        .collection(driverPayouts)
        .doc(withdrawHistory.id)
        .set(withdrawHistory.toJson())
        .then((value) {
      firestore
          .collection(driverPayouts)
          .doc(withdrawHistory.id)
          .get()
          .then((value) {
        DocumentSnapshot<Map<String, dynamic>> documentData = value;
        print(documentData.data());
      });
    });
    return "updated Amount".tr();
  }

  static Future updateCurrentUserWallet(
      {required String userId, required amount}) async {
    await firestore.collection(USERS).doc(userId).get().then((value) async {
      DocumentSnapshot<Map<String, dynamic>> userDocument = value;

      if (userDocument.data() != null && userDocument.exists) {
        try {
          print("--->amount---- $amount");
          print(userDocument.data());
          User user = User.fromJson(userDocument.data()!);
          MyAppState.currentUser = user;
          await firestore
              .collection(USERS)
              .doc(userId)
              .update({"wallet_amount": user.walletAmount + amount}).then(
                  (value) => print("north"));
          DocumentSnapshot<Map<String, dynamic>> newUserDocument =
              await firestore.collection(USERS).doc(userId).get();
          MyAppState.currentUser = User.fromJson(newUserDocument.data()!);
        } catch (error) {
          print(error);
          if (error.toString() ==
              "Bad state: field does not exist within the DocumentSnapshotPlatform") {
            print("does not exist");
          } else {
            print("went wrong!!");
          }
        }
        print("data val");
        return ""; //User.fromJson(userDocument.data()!);
      } else {
        return 0.111;
      }
    });
  }

  // static Future updateCompanyWalletAmount({required String companyId, required amount}) async {
  //   await firestore.collection(USERS).doc(companyId).get().then((value) async {
  //     DocumentSnapshot<Map<String, dynamic>> userDocument = value;
  //
  //     if (userDocument.data() != null && userDocument.exists) {
  //       try {
  //         print("--->amount---- $amount");
  //         print(userDocument.data());
  //         User user = User.fromJson(userDocument.data()!);
  //         await firestore.collection(USERS).doc(companyId).update({"wallet_amount": user.walletAmount + amount}).then((value) => print("north"));
  //       } catch (error) {
  //         print(error);
  //         if (error.toString() == "Bad state: field does not exist within the DocumentSnapshotPlatform") {
  //           print("does not exist");
  //         } else {
  //           print("went wrong!!");
  //         }
  //       }
  //       print("data val");
  //       return ""; //User.fromJson(userDocument.data()!);
  //     } else {
  //       return 0.111;
  //     }
  //   });
  // }

  static Future updateVendorAmount(
      {required String userId, required amount}) async {
    await firestore.collection(USERS).doc(userId).get().then((value) async {
      DocumentSnapshot<Map<String, dynamic>> userDocument = value;

      if (userDocument.data() != null && userDocument.exists) {
        try {
          print("--->amount---- $amount");
          print(userDocument.data());
          User user = User.fromJson(userDocument.data()!);
          await firestore
              .collection(USERS)
              .doc(userId)
              .update({"wallet_amount": user.walletAmount + amount}).then(
                  (value) => print("north"));
        } catch (error) {
          print(error);
          if (error.toString() ==
              "Bad state: field does not exist within the DocumentSnapshotPlatform") {
            print("does not exist");
          } else {
            print("went wrong!!");
          }
        }
        print("data val");
        return ""; //User.fromJson(userDocument.data()!);
      } else {
        return 0.111;
      }
    });
  }

  static Future<VendorModel?> getVendor(String vid) async {
    DocumentSnapshot<Map<String, dynamic>> userDocument =
        await firestore.collection(VENDORS).doc(vid).get();
    if (userDocument.data() != null && userDocument.exists) {
      print("dataaaaaa");
      return VendorModel.fromJson(userDocument.data()!);
    } else {
      print("nulllll");
      return null;
    }
  }

  static Future<String> uploadUserImageToFireStorage(
      File image, String userID) async {
    try {
      final FirebaseStorage storage = FirebaseStorage.instance;
      final Reference upload =
          storage.ref().child('ta_entregue/images/$userID.png');

      final UploadTask uploadTask = upload.putFile(
        image,
        SettableMetadata(contentType: 'image/png'),
      );

      final TaskSnapshot snapshot = await uploadTask.whenComplete(() {});
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      print('Erro ao fazer upload da imagem de perfil: $e');
      return 'ERRO_UPLOAD: Não foi possível enviar a imagem de perfil. Detalhe: $e';
    }
  }

  static Future<String> uploadCarImageToFireStorage(
    File image,
    String userID, [
    String? type = 'outher',
  ]) async {
    try {
      var uniqueID = const Uuid().v4();

      final storageRef = FirebaseStorage.instance
          .ref()
          .child('$STORAGE_ROOT/$type/$uniqueID.png');

      final uploadTask = storageRef.putFile(image);
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } on FirebaseException catch (e, stack) {
      print('FirebaseException: ${e.message}');
      print('Code: ${e.code}');
      print('Stack: $stack');
    } catch (e) {
      print('Erro geral: $e');
    }
    return '';
  }

  Future<String> uploadVideoThumbnailToFireStorage(File file) async {
    var uniqueID = const Uuid().v4();
    Reference upload = storage.child('$STORAGE_ROOT/thumbnails/$uniqueID.png');
    UploadTask uploadTask = upload.putFile(file);
    var downloadUrl =
        await (await uploadTask.whenComplete(() {})).ref.getDownloadURL();
    return downloadUrl.toString();
  }

  Stream<User> getUserByID(String id) async* {
    StreamController<User> userStreamController = StreamController();
    firestore.collection(USERS).doc(id).snapshots().listen((user) {
      try {
        User userModel = User.fromJson(user.data() ?? {});
        userStreamController.sink.add(userModel);
      } catch (e) {
        print(
            'FireStoreUtils.getUserByID failed to parse user object ${user.id}');
      }
    });
    yield* userStreamController.stream;
  }

  Future<bool> blockUser(User blockedUser, String type) async {
    bool isSuccessful = false;
    BlockUserModel blockUserModel = BlockUserModel(
        type: type,
        source: MyAppState.currentUser!.userID,
        dest: blockedUser.userID,
        createdAt: Timestamp.now());
    await firestore
        .collection(REPORTS)
        .add(blockUserModel.toJson())
        .then((onValue) {
      isSuccessful = true;
    });
    return isSuccessful;
  }

  Stream<bool> getBlocks() async* {
    StreamController<bool> refreshStreamController = StreamController();
    firestore
        .collection(REPORTS)
        .where('source', isEqualTo: MyAppState.currentUser!.userID)
        .snapshots()
        .listen((onData) {
      List<BlockUserModel> list = [];
      for (DocumentSnapshot<Map<String, dynamic>> block in onData.docs) {
        list.add(BlockUserModel.fromJson(block.data() ?? {}));
      }
      blockedList = list;
      refreshStreamController.sink.add(true);
    });
    yield* refreshStreamController.stream;
  }

  bool validateIfUserBlocked(String userID) {
    for (BlockUserModel blockedUser in blockedList) {
      if (userID == blockedUser.dest) {
        return true;
      }
    }
    return false;
  }

  Future<List<OrderModel>> getDriverOrders(String userID) async {
    try {
      QuerySnapshot<Map<String, dynamic>> ordersQuery = await firestore
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: userID)
          .orderBy('createdAt', descending: true)
          .get();

      final List<OrderModel> orderList = ordersQuery.docs
          .map((doc) => OrderModel.fromJson(doc.data()))
          .toList();

      return orderList;
    } catch (e) {
      print('Error getting driver orders: $e');
      return [];
    }
  }

  Future<List<User>> getRentalCompanyDriver(String companyId) async {
    List<User> driverList = [];

    QuerySnapshot<Map<String, dynamic>> ordersQuery = await firestore
        .collection(USERS)
        .where('companyId', isEqualTo: companyId)
        .where("serviceType", isEqualTo: "rental-service")
        .get();
    await Future.forEach(ordersQuery.docs,
        (QueryDocumentSnapshot<Map<String, dynamic>> document) {
      try {
        driverList.add(User.fromJson(document.data()));
      } catch (e, stacksTrace) {
        print('FireStoreUtils.getDriverOrders Parse error ${document.id} $e '
            '$stacksTrace');
      }
    });
    return driverList;
  }

  Future<List<User>> getCabCompanyDriver(String companyId) async {
    List<User> driverList = [];

    QuerySnapshot<Map<String, dynamic>> ordersQuery = await firestore
        .collection(USERS)
        .where('companyId', isEqualTo: companyId)
        .where("serviceType", isEqualTo: "cab-service")
        .get();
    await Future.forEach(ordersQuery.docs,
        (QueryDocumentSnapshot<Map<String, dynamic>> document) {
      try {
        driverList.add(User.fromJson(document.data()));
      } catch (e, stacksTrace) {
        print('FireStoreUtils.getDriverOrders Parse error ${document.id} $e '
            '$stacksTrace');
      }
    });
    return driverList;
  }

  Future<List<CabOrderModel>> getCabDriverOrders(String userID) async {
    List<CabOrderModel> orders = [];

    QuerySnapshot<Map<String, dynamic>> ordersQuery = await firestore
        .collection(RIDESORDER)
        .where('driverID', isEqualTo: userID)
        .orderBy('createdAt', descending: true)
        .get();
    await Future.forEach(ordersQuery.docs,
        (QueryDocumentSnapshot<Map<String, dynamic>> document) {
      try {
        orders.add(CabOrderModel.fromJson(document.data()));
      } catch (e, stacksTrace) {
        print('FireStoreUtils.getDriverOrders Parse error ${document.id} $e '
            '$stacksTrace');
      }
    });
    return orders;
  }

  static Future updateOrder(OrderModel orderModel) async {
    await firestore
        .collection(ORDERS)
        .doc(orderModel.id)
        .set(orderModel.toJson(), SetOptions(merge: true));
  }

  static Future<SectionModel?> getSectionBySectionId(String uid) async {
    DocumentSnapshot<Map<String, dynamic>> userDocument =
        await firestore.collection(SECTION).doc(uid).get();
    if (userDocument.data() != null && userDocument.exists) {
      // print('milaa');

      return SectionModel.fromJson(userDocument.data()!);
    } else {
      return null;
    }
  }

  static Future<bool> getCabFirstOrderOrNOt(CabOrderModel orderModel) async {
    bool isFirst = true;
    await firestore
        .collection(RIDESORDER)
        .where('authorID', isEqualTo: orderModel.authorID)
        .get()
        .then((value) {
      if (value.size == 1) {
        isFirst = true;
      } else {
        isFirst = false;
      }
    });
    return isFirst;
  }

  static Future updateCabReferralAmount(CabOrderModel orderModel) async {
    ReferralModel? referralModel;
    SectionModel? sectionModel;
    print(orderModel.authorID);
    await getSectionBySectionId(orderModel.sectionId.toString()).then((value) {
      sectionModel = value;
    });
    await firestore
        .collection(REFERRAL)
        .doc(orderModel.authorID)
        .get()
        .then((value) {
      if (value.data() != null) {
        referralModel = ReferralModel.fromJson(value.data()!);
      } else {
        return;
      }
    });

    if (referralModel != null) {
      if (referralModel!.referralBy != null &&
          referralModel!.referralBy!.isNotEmpty) {
        await firestore
            .collection(USERS)
            .doc(referralModel!.referralBy)
            .get()
            .then((value) async {
          DocumentSnapshot<Map<String, dynamic>> userDocument = value;
          if (userDocument.data() != null && userDocument.exists) {
            try {
              print(userDocument.data());
              User user = User.fromJson(userDocument.data()!);
              await firestore.collection(USERS).doc(user.userID).update({
                "wallet_amount": user.walletAmount +
                    double.parse(sectionModel!.referralAmount.toString())
              }).then((value) => print("north"));

              await FireStoreUtils.createPaymentId().then((value) async {
                final paymentID = value;
                await FireStoreUtils.topUpWalletAmountRefral(
                    paymentMethod: "Referral Amount",
                    amount:
                        double.parse(sectionModel!.referralAmount.toString()),
                    id: paymentID,
                    userId: referralModel!.referralBy);
              });
            } catch (error) {
              print(error);
              if (error.toString() ==
                  "Bad state: field does not exist within the DocumentSnapshotPlatform") {
                print("does not exist");
                //await firestore.collection(USERS).doc(userId).update({"wallet_amount": 0});
                //walletAmount = 0;
              } else {
                print("went wrong!!");
              }
            }
            print("data val");
          }
        });
      } else {
        return;
      }
    }
  }

  static Future<bool> getFirestOrderOrNOt(OrderModel orderModel) async {
    bool isFirst = true;
    await firestore
        .collection(ORDERS)
        .where('authorID', isEqualTo: orderModel.authorID)
        .where('section_id', isEqualTo: orderModel.sectionId)
        .get()
        .then((value) {
      if (value.size == 1) {
        isFirst = true;
      } else {
        isFirst = false;
      }
    });
    return isFirst;
  }

  static Future updateReferralAmount(OrderModel orderModel) async {
    ReferralModel? referralModel;
    print(orderModel.authorID);

    await getSectionBySectionId(orderModel.id).then((valueSection) async {
      await firestore
          .collection(REFERRAL)
          .doc(orderModel.authorID)
          .get()
          .then((value) {
        if (value.data() != null) {
          referralModel = ReferralModel.fromJson(value.data()!);
        } else {
          return;
        }
      });

      print("refferealAMount----->${valueSection!.referralAmount.toString()}");
      print("refferealAMount----->${referralModel!.referralBy}");

      if (referralModel != null) {
        if (referralModel!.referralBy != null &&
            referralModel!.referralBy!.isNotEmpty) {
          await firestore
              .collection(USERS)
              .doc(referralModel!.referralBy)
              .get()
              .then((value) async {
            DocumentSnapshot<Map<String, dynamic>> userDocument = value;
            if (userDocument.data() != null && userDocument.exists) {
              try {
                print(userDocument.data());
                User user = User.fromJson(userDocument.data()!);
                await firestore.collection(USERS).doc(user.userID).update({
                  "wallet_amount": user.walletAmount +
                      double.parse(valueSection.referralAmount.toString())
                }).then((value) => print("north"));

                await FireStoreUtils.createPaymentId().then((value) async {
                  final paymentID = value;
                  await FireStoreUtils.topUpWalletAmountRefral(
                      paymentMethod: "Referral Amount",
                      amount:
                          double.parse(valueSection.referralAmount.toString()),
                      id: paymentID,
                      userId: referralModel!.referralBy);
                });
              } catch (error) {
                print(error);
                if (error.toString() ==
                    "Bad state: field does not exist within the DocumentSnapshotPlatform") {
                  print("does not exist");
                  //await firestore.collection(USERS).doc(userId).update({"wallet_amount": 0});
                  //walletAmount = 0;
                } else {
                  print("went wrong!!");
                }
              }
              print("data val");
            }
          });
        } else {
          return;
        }
      }
    });
  }

  static Future<bool> getFirestOrderOrNOtCabService(
      CabOrderModel orderModel) async {
    bool isFirst = true;
    await firestore
        .collection(ORDERS)
        .where('authorID', isEqualTo: orderModel.authorID)
        .get()
        .then((value) {
      if (value.size == 1) {
        isFirst = true;
      } else {
        isFirst = false;
      }
    });
    return isFirst;
  }

  static Future updateReferralAmountCabService(CabOrderModel orderModel) async {
    ReferralModel? referralModel;
    SectionModel? sectionModel;
    print(orderModel.authorID);
    await getSectionBySectionId(orderModel.sectionId.toString()).then((value) {
      sectionModel = value;
    });
    await firestore
        .collection(REFERRAL)
        .doc(orderModel.authorID)
        .get()
        .then((value) {
      if (value.data() != null) {
        referralModel = ReferralModel.fromJson(value.data()!);
      } else {
        return;
      }
    });

    if (referralModel != null) {
      if (referralModel!.referralBy != null &&
          referralModel!.referralBy!.isNotEmpty) {
        await firestore
            .collection(USERS)
            .doc(referralModel!.referralBy)
            .get()
            .then((value) async {
          DocumentSnapshot<Map<String, dynamic>> userDocument = value;
          if (userDocument.data() != null && userDocument.exists) {
            try {
              print(userDocument.data());
              User user = User.fromJson(userDocument.data()!);
              await firestore.collection(USERS).doc(user.userID).update({
                "wallet_amount": user.walletAmount +
                    double.parse(sectionModel!.referralAmount.toString())
              }).then((value) => print("north"));

              await FireStoreUtils.createPaymentId().then((value) async {
                final paymentID = value;
                await FireStoreUtils.topUpWalletAmountRefral(
                    paymentMethod: "Referral Amount",
                    amount:
                        double.parse(sectionModel!.referralAmount.toString()),
                    id: paymentID,
                    userId: referralModel!.referralBy);
              });
            } catch (error) {
              print(error);
              if (error.toString() ==
                  "Bad state: field does not exist within the DocumentSnapshotPlatform") {
                print("does not exist");
                //await firestore.collection(USERS).doc(userId).update({"wallet_amount": 0});
                //walletAmount = 0;
              } else {
                print("went wrong!!");
              }
            }
            print("data val");
          }
        });
      } else {
        return;
      }
    }
  }

  static sendTopUpMail(
      {required String amount,
      required String paymentMethod,
      required String tractionId}) async {
    EmailTemplateModel? emailTemplateModel =
        await FireStoreUtils.getEmailTemplates(walletTopup);

    String newString = emailTemplateModel!.message.toString();
    newString = newString.replaceAll("{username}",
        "${MyAppState.currentUser!.firstName} ${MyAppState.currentUser!.lastName}");
    newString = newString.replaceAll(
        "{date}", DateFormat('dd-MM-yyyy').format(Timestamp.now().toDate()));
    newString = newString.replaceAll("{amount}", amountShow(amount: amount));
    newString =
        newString.replaceAll("{paymentmethod}", paymentMethod.toString());
    newString = newString.replaceAll("{transactionid}", tractionId.toString());
    newString = newString.replaceAll("{newwalletbalance}.",
        amountShow(amount: MyAppState.currentUser!.walletAmount.toString()));
    await sendMail(
        subject: emailTemplateModel.subject,
        isAdmin: emailTemplateModel.isSendToAdmin,
        body: newString,
        recipients: [MyAppState.currentUser!.email]);
  }

  static sendPayoutMail(
      {required String amount, required String payoutrequestid}) async {
    EmailTemplateModel? emailTemplateModel =
        await FireStoreUtils.getEmailTemplates(payoutRequest);

    String body = emailTemplateModel!.subject.toString();
    body = body.replaceAll("{userid}", MyAppState.currentUser!.userID);

    String newString = emailTemplateModel.message.toString();
    newString = newString.replaceAll("{username}",
        "${MyAppState.currentUser!.firstName} ${MyAppState.currentUser!.lastName}");
    newString =
        newString.replaceAll("{userid}", MyAppState.currentUser!.userID);
    newString = newString.replaceAll("{amount}", amountShow(amount: amount));
    newString = newString.replaceAll(
        "{date}", DateFormat('dd-MM-yyyy').format(Timestamp.now().toDate()));
    newString =
        newString.replaceAll("{payoutrequestid}", payoutrequestid.toString());
    newString = newString.replaceAll("{usercontactinfo}",
        "${MyAppState.currentUser!.email}\n${MyAppState.currentUser!.phoneNumber}");
    await sendMail(
        subject: body,
        isAdmin: emailTemplateModel.isSendToAdmin,
        body: newString,
        recipients: [MyAppState.currentUser!.email]);
  }

  static Future<EmailTemplateModel?> getEmailTemplates(String type) async {
    EmailTemplateModel? emailTemplateModel;
    await firestore
        .collection(emailTemplates)
        .where('type', isEqualTo: type)
        .get()
        .then((value) {
      print("------>");
      if (value.docs.isNotEmpty) {
        print(value.docs.first.data());
        emailTemplateModel =
            EmailTemplateModel.fromJson(value.docs.first.data());
      }
    });
    return emailTemplateModel;
  }

  static Future topUpWalletAmountRefral(
      {String paymentMethod = "test",
      bool isTopup = true,
      required amount,
      required id,
      orderId = "",
      userId}) async {
    print("this is te payment id");
    print(id);
    print(userId);

    await firestore.collection(Wallet).doc(id).set({
      "user_id": userId,
      "payment_method": paymentMethod,
      "amount": amount,
      "id": id,
      "order_id": orderId,
      "isTopUp": isTopup,
      "payment_status": "success",
      "date": DateTime.now(),
      "transactionUser": "driver",
    }).then((value) {
      firestore.collection(Wallet).doc(id).get().then((value) {
        DocumentSnapshot<Map<String, dynamic>> documentData = value;
        print("nato");
        print(documentData.data());
      });
    });

    return "updated Amount".tr();
  }

  static Future updateCabOrder(CabOrderModel orderModel) async {
    print("------->${orderModel.adminCommission}");
    await firestore
        .collection(RIDESORDER)
        .doc(orderModel.id)
        .set(orderModel.toJson(), SetOptions(merge: true));
  }

  late StreamController<OrderModel> ordersStreamController;
  late StreamSubscription ordersStreamSub;

  Stream<OrderModel?> getOrderByID(String inProgressOrderID) async* {
    ordersStreamController = StreamController();
    ordersStreamSub = firestore
        .collection(ORDERS)
        .doc(inProgressOrderID)
        .snapshots()
        .listen((onData) async {
      if (onData.data() != null) {
        OrderModel? orderModel = OrderModel.fromJson(onData.data()!);
        ordersStreamController.sink.add(orderModel);
      }
    });
    yield* ordersStreamController.stream;
  }

  late StreamController<CabOrderModel> cabOrdersStreamController;
  late StreamSubscription cabOrdersStreamSub;

  Stream<CabOrderModel?> getCabOrderByID(String inProgressOrderID) async* {
    cabOrdersStreamController = StreamController();
    cabOrdersStreamSub = firestore
        .collection(RIDESORDER)
        .doc(inProgressOrderID)
        .snapshots()
        .listen((onData) async {
      if (onData.data() != null) {
        CabOrderModel? orderModel = CabOrderModel.fromJson(onData.data()!);
        cabOrdersStreamController.sink.add(orderModel);
      }
    });
    yield* cabOrdersStreamController.stream;
  }

  /// compress video file to make it load faster but with lower quality,
  /// change the quality parameter to control the quality of the video after
  /// being compressed
  /// @param file the video file that will be compressed
  /// @return File a new compressed file with smaller size
  Future<File> _compressVideo(File file) async {
    MediaInfo? info = await VideoCompress.compressVideo(file.path,
        quality: VideoQuality.DefaultQuality,
        deleteOrigin: false,
        includeAudio: true,
        frameRate: 24);
    if (info != null) {
      File compressedVideo = File(info.path!);
      return compressedVideo;
    } else {
      return file;
    }
  }

  static loginWithFacebook() async {
    /// creates a user for this facebook login when this user first time login
    /// and save the new user object to firebase and firebase auth
    FacebookAuth facebookAuth = FacebookAuth.instance;
    bool isLogged = await facebookAuth.accessToken != null;
    if (!isLogged) {
      LoginResult result = await facebookAuth
          .login(); // by default we request the email and the public profile
      if (result.status == LoginStatus.success) {
        // you are logged
        AccessToken? token = await facebookAuth.accessToken;
        return await handleFacebookLogin(
            await facebookAuth.getUserData(), token!);
      }
    } else {
      AccessToken? token = await facebookAuth.accessToken;
      return await handleFacebookLogin(
          await facebookAuth.getUserData(), token!);
    }
  }

  static handleFacebookLogin(
      Map<String, dynamic> userData, AccessToken token) async {
    auth.UserCredential authResult = await auth.FirebaseAuth.instance
        .signInWithCredential(
            auth.FacebookAuthProvider.credential(token.tokenString));
    print(authResult.user!.uid);
    User? user = await getCurrentUser(authResult.user?.uid ?? '');
    List<String> fullName = (userData['name'] as String).split(' ');
    String firstName = '';
    String lastName = '';
    if (fullName.isNotEmpty) {
      firstName = fullName.first;
      lastName = fullName.skip(1).join(' ');
    }
    if (user != null && user.role == USER_ROLE_DRIVER) {
      print('if');
      user.profilePictureURL = userData['picture']['data']['url'];
      user.firstName = firstName;
      user.lastName = lastName;
      user.email = userData['email'];
      user.isActive = false;
      user.role = USER_ROLE_DRIVER;
      user.fcmToken = await firebaseMessaging.getToken() ?? '';
      dynamic result = await updateCurrentUser(user);
      return result;
    } else if (user == null) {
      print('else');
      user = User(
          email: userData['email'] ?? '',
          firstName: firstName,
          profilePictureURL: userData['picture']['data']['url'] ?? '',
          userID: authResult.user?.uid ?? '',
          lastOnlineTimestamp: Timestamp.now(),
          lastName: lastName,
          isActive: true,
          role: USER_ROLE_DRIVER,
          fcmToken: await firebaseMessaging.getToken() ?? '',
          phoneNumber: '',
          carName: 'Uber Car',
          carNumber: 'No Plates',
          carPictureURL: DEFAULT_CAR_IMAGE,
          teste_entregador: true,
          settings: UserSettings());
      String? errorMessage = await firebaseCreateNewUser(user);
      if (errorMessage == null) {
        return user;
      } else {
        return errorMessage;
      }
    }
  }

  static loginWithApple() async {
    final appleCredential = await apple.TheAppleSignIn.performRequests([
      const apple.AppleIdRequest(
          requestedScopes: [apple.Scope.email, apple.Scope.fullName])
    ]);
    if (appleCredential.error != null) {
      return "Couldn't login with apple.".tr();
    }

    if (appleCredential.status == apple.AuthorizationStatus.authorized) {
      final auth.AuthCredential credential =
          auth.OAuthProvider('apple.com').credential(
        accessToken: String.fromCharCodes(
            appleCredential.credential?.authorizationCode ?? []),
        idToken: String.fromCharCodes(
            appleCredential.credential?.identityToken ?? []),
      );
      return await handleAppleLogin(credential, appleCredential.credential!);
    } else {
      return "Couldn't login with apple.".tr();
    }
  }

  static handleAppleLogin(
    auth.AuthCredential credential,
    apple.AppleIdCredential appleIdCredential,
  ) async {
    auth.UserCredential authResult =
        await auth.FirebaseAuth.instance.signInWithCredential(credential);
    User? user = await getCurrentUser(authResult.user?.uid ?? '');
    if (user != null) {
      user.isActive = false;
      user.role = USER_ROLE_DRIVER;
      user.fcmToken = await firebaseMessaging.getToken() ?? '';
      dynamic result = await updateCurrentUser(user);
      return result;
    } else {
      user = User(
          email: appleIdCredential.email ?? '',
          firstName: appleIdCredential.fullName?.givenName ?? '',
          profilePictureURL: '',
          userID: authResult.user?.uid ?? '',
          lastOnlineTimestamp: Timestamp.now(),
          lastName: appleIdCredential.fullName?.familyName ?? '',
          role: USER_ROLE_DRIVER,
          active: true,
          isActive: true,
          fcmToken: await firebaseMessaging.getToken() ?? '',
          phoneNumber: '',
          carName: 'Uber Car',
          carNumber: 'No Plates',
          carPictureURL: DEFAULT_CAR_IMAGE,
          teste_entregador: true,
          settings: UserSettings());
      String? errorMessage = await firebaseCreateNewUser(user);
      if (errorMessage == null) {
        return user;
      } else {
        return errorMessage;
      }
    }
  }

  /// save a new user document in the USERS table in firebase firestore
  /// returns an error message on failure or null on success
  static Future<String?> firebaseCreateNewUser(User user) async {
    try {
      await firestore.collection(USERS).doc(user.userID).set(user.toJson());
    } catch (e, s) {
      print('FireStoreUtils.firebaseCreateNewUser $e $s');
      return "Couldn't sign up".tr();
    }
    return null;
  }

  /// login with email and password with firebase
  /// @param email user email
  /// @param password user password
  static Future<dynamic> loginWithEmailAndPassword(
      String email, String password) async {
    try {
      print('FireStoreUtils.loginWithEmailAndPassword');
      auth.UserCredential result = await auth.FirebaseAuth.instance
          .signInWithEmailAndPassword(email: email, password: password);
      DocumentSnapshot<Map<String, dynamic>> documentSnapshot =
          await firestore.collection(USERS).doc(result.user?.uid ?? '').get();
      User? user;
      if (documentSnapshot.exists) {
        user = User.fromJson(documentSnapshot.data() ?? {});
        // Try to get FCM token, but handle the case when APNS token is not available yet
        user.fcmToken = await _getSafeFCMToken();
        user.isActive = false;
      }
      return user;
    } on auth.FirebaseAuthException catch (exception, s) {
      print('$exception$s');
      switch ((exception).code) {
        case 'invalid-email':
          return 'Email address is malformed.'.tr();
        case 'wrong-password':
          return "Wrong password.".tr();
        case 'user-not-found':
          return 'No user corresponding to the given email address.'.tr();
        case 'user-disabled':
          return 'This user has been disabled.'.tr();
        case 'too-many-requests':
          return 'Too many attempts to sign in as this user.'.tr();
      }
      return 'Unexpected firebase error, Please try again.'.tr();
    } catch (e, s) {
      print('$e$s');
      return 'Login failed, Please try again.'.tr();
    }
  }

  ///submit a phone number to firebase to receive a code verification, will
  ///be used later to login
  static firebaseSubmitPhoneNumber(
    String phoneNumber,
    auth.PhoneCodeAutoRetrievalTimeout? phoneCodeAutoRetrievalTimeout,
    auth.PhoneCodeSent? phoneCodeSent,
    auth.PhoneVerificationFailed? phoneVerificationFailed,
    auth.PhoneVerificationCompleted? phoneVerificationCompleted,
  ) {
    auth.FirebaseAuth.instance.verifyPhoneNumber(
      timeout: const Duration(minutes: 2),
      phoneNumber: phoneNumber,
      verificationCompleted: phoneVerificationCompleted!,
      verificationFailed: phoneVerificationFailed!,
      codeSent: phoneCodeSent!,
      codeAutoRetrievalTimeout: phoneCodeAutoRetrievalTimeout!,
    );
  }

  /// submit the received code to firebase to complete the phone number
  /// verification process
  static Future<dynamic> firebaseSubmitPhoneNumberCode(
      String verificationID, String code, String phoneNumber,
      {String firstName = 'Anonymous',
      String lastName = 'User',
      File? image,
      File? carImage,
      String carName = '',
      String carPlates = '',
      String? serviceType = ''}) async {
    auth.AuthCredential authCredential = auth.PhoneAuthProvider.credential(
        verificationId: verificationID, smsCode: code);
    auth.UserCredential userCredential =
        await auth.FirebaseAuth.instance.signInWithCredential(authCredential);
    User? user = await getCurrentUser(userCredential.user?.uid ?? '');
    if (user != null && user.role == USER_ROLE_DRIVER) {
      // Try to get FCM token, but handle the case when APNS token is not available yet
      user.fcmToken = await _getSafeFCMToken();
      user.role = USER_ROLE_DRIVER;
      user.isActive = false;
      await updateCurrentUser(user);
      return user;
    } else if (user == null) {
      /// create a new user from phone login
      String profileImageUrl = '';
      String carPicUrl = DEFAULT_CAR_IMAGE;
      if (image != null) {
        profileImageUrl = await uploadUserImageToFireStorage(
            image, userCredential.user?.uid ?? '');
      }
      if (carImage != null) {
        carPicUrl = await uploadCarImageToFireStorage(
            carImage, userCredential.user?.uid ?? '', 'car');
      }
      User user = User(
          firstName: firstName,
          lastName: lastName,
          fcmToken: await _getSafeFCMToken(),
          phoneNumber: phoneNumber,
          profilePictureURL: profileImageUrl,
          userID: userCredential.user?.uid ?? '',
          isActive: true,
          active: true,
          lastOnlineTimestamp: Timestamp.now(),
          settings: UserSettings(),
          email: '',
          role: USER_ROLE_DRIVER,
          carName: carName,
          carNumber: carPlates,
          carPictureURL: carPicUrl,
          serviceType: serviceType.toString());
      String? errorMessage = await firebaseCreateNewUser(user);
      if (errorMessage == null) {
        return user;
      } else {
        return "Couldn't create new user with phone number.".tr();
      }
    }
  }

  static Future<dynamic> firebaseSubmitPhoneNumberCodeParcelService(
      String verificationID, String code, String phoneNumber,
      {String firstName = 'Anonymous',
      String lastName = 'User',
      File? image,
      File? carImage,
      String carName = '',
      String carPlates = '',
      String? serviceType = ''}) async {
    auth.AuthCredential authCredential = auth.PhoneAuthProvider.credential(
        verificationId: verificationID, smsCode: code);
    auth.UserCredential userCredential =
        await auth.FirebaseAuth.instance.signInWithCredential(authCredential);
    User? user = await getCurrentUser(userCredential.user?.uid ?? '');
    if (user != null && user.role == USER_ROLE_DRIVER) {
      try {
        // Try to get FCM token, but handle the case when APNS token is not available yet
        user.fcmToken = await firebaseMessaging.getToken() ?? '';
      } catch (e) {
        print('Failed to get FCM token: $e');
        // Keep existing token if token retrieval fails
      }
      user.role = USER_ROLE_DRIVER;
      user.isActive = false;
      await updateCurrentUser(user);
      return user;
    } else if (user == null) {
      /// create a new user from phone login
      String profileImageUrl = '';
      String carPicUrl = DEFAULT_CAR_IMAGE;
      if (image != null) {
        profileImageUrl = await uploadUserImageToFireStorage(
            image, userCredential.user?.uid ?? '');
      }
      if (carImage != null) {
        carPicUrl = await uploadCarImageToFireStorage(
            carImage, userCredential.user?.uid ?? '', 'car');
      }
      User user = User(
          firstName: firstName,
          lastName: lastName,
          fcmToken: await _getSafeFCMToken(),
          phoneNumber: phoneNumber,
          profilePictureURL: profileImageUrl,
          userID: userCredential.user?.uid ?? '',
          isActive: true,
          active: true,
          lastOnlineTimestamp: Timestamp.now(),
          settings: UserSettings(),
          email: '',
          role: USER_ROLE_DRIVER,
          carName: carName,
          carNumber: carPlates,
          carPictureURL: carPicUrl,
          serviceType: serviceType.toString());
      String? errorMessage = await firebaseCreateNewUser(user);
      if (errorMessage == null) {
        return user;
      } else {
        return "Couldn't create new user with phone number.".tr();
      }
    }
  }

  static Future<dynamic> firebaseSubmitPhoneNumberCodeCabService(
      String verificationID, String code, String phoneNumber,
      {String firstName = 'Anonymous',
      String lastName = 'User',
      File? image,
      File? carImage,
      String carMakes = '',
      String carName = '',
      String carPlates = '',
      String? vehicleType = '',
      String? serviceType = ''}) async {
    auth.AuthCredential authCredential = auth.PhoneAuthProvider.credential(
        verificationId: verificationID, smsCode: code);
    auth.UserCredential userCredential =
        await auth.FirebaseAuth.instance.signInWithCredential(authCredential);
    User? user = await getCurrentUser(userCredential.user?.uid ?? '');
    if (user != null && user.role == USER_ROLE_DRIVER) {
      // Try to get FCM token, but handle the case when APNS token is not available yet
      user.fcmToken = await _getSafeFCMToken();
      user.role = USER_ROLE_DRIVER;
      user.isActive = false;
      await updateCurrentUser(user);
      return user;
    } else if (user == null) {
      /// create a new user from phone login
      String profileImageUrl = '';
      String carPicUrl = DEFAULT_CAR_IMAGE;
      if (image != null) {
        profileImageUrl = await uploadUserImageToFireStorage(
            image, userCredential.user?.uid ?? '');
      }
      if (carImage != null) {
        carPicUrl = await uploadCarImageToFireStorage(
            carImage, userCredential.user?.uid ?? '', carMakes);
      }
      User user = User(
        firstName: firstName,
        lastName: lastName,
        fcmToken: await _getSafeFCMToken(),
        phoneNumber: phoneNumber,
        profilePictureURL: profileImageUrl,
        userID: userCredential.user?.uid ?? '',
        isActive: true,
        active: true,
        lastOnlineTimestamp: Timestamp.now(),
        settings: UserSettings(),
        email: '',
        role: USER_ROLE_DRIVER,
        carName: carName,
        carMakes: carMakes,
        carNumber: carPlates,
        carPictureURL: carPicUrl,
        vehicleType: vehicleType.toString(),
        serviceType: serviceType.toString(),
      );
      String? errorMessage = await firebaseCreateNewUser(user);
      if (errorMessage == null) {
        return user;
      } else {
        return "Couldn't create new user with phone number.".tr();
      }
    }
  }

  static firebaseSignUpWithEmailAndPassword(
      String emailAddress,
      String cpfCnpj,
      String password,
      File? image,
      File? carImage,
      String carName,
      String carPlate,
      String firstName,
      String lastName,
      String mobile,
      String serviceType) async {
    try {
      auth.UserCredential result = await auth.FirebaseAuth.instance
          .createUserWithEmailAndPassword(
              email: emailAddress, password: password);
      String profilePicUrl = '';
      String carPicUrl = DEFAULT_CAR_IMAGE;
      String driverProofUrl = '';
      String carProofUrl = '';

      String documentUrl = '';
      String criminalRecordUrl = '';
      String proofOfAddressUrl = '';

      if (image != null) {
        profilePicUrl = await uploadCarImageToFireStorage(
            image, result.user?.uid ?? '', 'profile');
      }

      if (carImage != null) {
        carPicUrl = await uploadCarImageToFireStorage(
            carImage, result.user?.uid ?? '', 'car');
      }

      // if (documentFile != null) {
      //   documentUrl = await uploadCarImageToFireStorage(
      //       documentFile, result.user?.uid ?? '', 'document');
      // }
      // if (criminalRecordFile != null) {
      //   criminalRecordUrl = await uploadCarImageToFireStorage(
      //       criminalRecordFile, result.user?.uid ?? '', 'criminalRecord');
      // }
      // if (proofOfAddressFile != null) {
      //   proofOfAddressUrl = await uploadCarImageToFireStorage(
      //       proofOfAddressFile, result.user?.uid ?? '', 'proofOfAddress');
      // }

      User user = User(
        email: emailAddress,
        cpf: cpfCnpj,
        settings: UserSettings(),
        lastOnlineTimestamp: Timestamp.now(),
        isActive: true,
        active: true,
        phoneNumber: mobile,
        firstName: firstName,
        userID: result.user?.uid ?? '',
        lastName: lastName,
        fcmToken: await _getSafeFCMToken(),
        profilePictureURL: profilePicUrl,
        carPictureURL: carPicUrl,
        carNumber: carPlate,
        carName: carName,
        role: USER_ROLE_DRIVER,
        serviceType: serviceType,
        carProofPictureURL: carProofUrl,
        driverProofPictureURL: driverProofUrl,
        createdAt: Timestamp.now(),
        documentUrl: '',
        document_accept: DocumentStatus.sent,
        criminalRecordUrl: '',
        criminalRecord_accept: DocumentStatus.sent,
        proofOfAddressUrl: '',
        proofOfAddress_accept: DocumentStatus.sent,
      );
      String? errorMessage = await firebaseCreateNewUser(user);
      if (errorMessage == null) {
        return user;
      } else {
        return "Couldn't sign up for firebase, Please try again.".tr();
      }
    } on auth.FirebaseAuthException catch (error) {
      print('$error${error.stackTrace}');
      String message = "Couldn't sign up".tr();
      switch (error.code) {
        case 'email-already-in-use':
          message = 'Email already in use, Please pick another email!'.tr();
          break;
        case 'invalid-email':
          message = 'Enter valid e-mail'.tr();
          break;
        case 'operation-not-allowed':
          message = 'Email/password accounts are not enabled'.tr();
          break;
        case 'weak-password':
          message = 'Password must be more than 5 characters'.tr();
          break;
        case 'too-many-requests':
          message = 'Too many requests, Please try again later.'.tr();
          break;
      }
      return message;
    } catch (e) {
      return "Couldn't sign up".tr();
    }
  }

  static firebaseSignUpWithEmailAndPasswordRentalService(
      String emailAddress,
      String password,
      File? image,
      File? carImage,
      File? driverProofImage,
      File? carProofImage,
      String carName,
      String carPlate,
      String firstName,
      String lastName,
      String mobile,
      String serviceType,
      String vehicleType,
      String companyOrNot,
      String companyName,
      String companyAddress) async {
    try {
      auth.UserCredential result = await auth.FirebaseAuth.instance
          .createUserWithEmailAndPassword(
              email: emailAddress, password: password);
      String profilePicUrl = '';
      String carPicUrl = DEFAULT_CAR_IMAGE;
      String driverProofUrl = '';
      String carProofUrl = '';

      User user = User(
        email: emailAddress,
        settings: UserSettings(),
        lastOnlineTimestamp: Timestamp.now(),
        isActive: true,
        active: true,
        phoneNumber: mobile,
        firstName: firstName,
        userID: result.user?.uid ?? '',
        lastName: lastName,
        fcmToken: await _getSafeFCMToken(),
        profilePictureURL: profilePicUrl,
        carPictureURL: carPicUrl,
        carNumber: carPlate,
        carName: carName,
        // isCompany: companyOrNot == "company" ? true : false,
        // companyName: companyName,
        // companyAddress: companyAddress,
        role: USER_ROLE_DRIVER,
        serviceType: serviceType,
        vehicleType: vehicleType,
        carProofPictureURL: carProofUrl,
        driverProofPictureURL: driverProofUrl,
        createdAt: Timestamp.now(),
      );
      String? errorMessage = await firebaseCreateNewUser(user);
      if (errorMessage == null) {
        return user;
      } else {
        return "Couldn't sign up for firebase, Please try again.".tr();
      }
    } on auth.FirebaseAuthException catch (error) {
      print('$error${error.stackTrace}');
      String message = "Couldn't sign up".tr();
      switch (error.code) {
        case 'email-already-in-use':
          message = 'Email already in use, Please pick another email!'.tr();
          break;
        case 'invalid-email':
          message = 'Enter valid e-mail'.tr();
          break;
        case 'operation-not-allowed':
          message = 'Email/password accounts are not enabled'.tr();
          break;
        case 'weak-password':
          message = 'Password must be more than 5 characters'.tr();
          break;
        case 'too-many-requests':
          message = 'Too many requests, Please try again later.'.tr();
          break;
      }
      return message;
    } catch (e) {
      return "Couldn't sign up".tr();
    }
  }

  static firebaseSignUpWithEmailAndPasswordCabService(
    String emailAddress,
    String password,
    File? image,
    File? carImage,
    File? driverProofImage,
    File? carProofImage,
    String vehicleType,
    String carMakes,
    String carModel,
    String carPlate,
    String carColor,
    String firstName,
    String lastName,
    String mobile,
    String serviceType,
    String companyOrNot,
    String companyName,
    String companyAddress,
    String sectionId,
    String vehicleId,
  ) async {
    try {
      auth.UserCredential result = await auth.FirebaseAuth.instance
          .createUserWithEmailAndPassword(
              email: emailAddress, password: password);
      String profilePicUrl = '';
      String carPicUrl = DEFAULT_CAR_IMAGE;
      String driverProofUrl = '';
      String carProofUrl = '';
      if (image != null) {
        profilePicUrl =
            await uploadUserImageToFireStorage(image, result.user?.uid ?? '');
      }
      if (carImage != null) {
        carPicUrl = await uploadCarImageToFireStorage(
            carImage, result.user?.uid ?? '', 'car');
      }

      if (driverProofImage != null) {
        driverProofUrl = await uploadCarImageToFireStorage(
            driverProofImage, Timestamp.now().toString(), 'driverProof');
      }
      if (carProofImage != null) {
        carProofUrl = await uploadCarImageToFireStorage(
            carProofImage, Timestamp.now().toString(), 'carProof');
      }

      User user = User(
          email: emailAddress,
          settings: UserSettings(),
          lastOnlineTimestamp: Timestamp.now(),
          isActive: true,
          active: true,
          phoneNumber: mobile,
          firstName: firstName,
          userID: result.user?.uid ?? '',
          lastName: lastName,
          fcmToken: await _getSafeFCMToken(),
          profilePictureURL: profilePicUrl,
          carPictureURL: carPicUrl,
          carNumber: carPlate,
          carName: carModel,
          carMakes: carMakes,
          vehicleType: vehicleType,
          serviceType: serviceType,
          role: USER_ROLE_DRIVER,
          // isCompany: companyOrNot == "company" ? true : false,
          // companyName: companyName,
          // companyAddress: companyAddress,
          carProofPictureURL: carProofUrl,
          driverProofPictureURL: driverProofUrl,
          carColor: carColor,
          sectionId: sectionId,
          rideType: 'ride',
          vehicleId: vehicleId,
          createdAt: Timestamp.now());
      String? errorMessage = await firebaseCreateNewUser(user);
      if (errorMessage == null) {
        return user;
      } else {
        return 'Couldn\'t sign up for firebase, Please try again.'.tr();
      }
    } on auth.FirebaseAuthException catch (error) {
      print('$error${error.stackTrace}');
      String message = 'Couldn\'t sign up';
      switch (error.code) {
        case 'email-already-in-use':
          message = 'Email already in use, Please pick another email!';
          break;
        case 'invalid-email':
          message = 'Enter valid e-mail';
          break;
        case 'operation-not-allowed':
          message = 'Email/password accounts are not enabled';
          break;
        case 'weak-password':
          message = 'Password must be more than 5 characters';
          break;
        case 'too-many-requests':
          message = 'Too many requests, Please try again later.';
          break;
      }
      return message;
    } catch (e) {
      return 'Couldn\'t sign up';
    }
  }

  static Future<auth.UserCredential?> reAuthUser(AuthProviders provider,
      {String? email,
      String? password,
      String? smsCode,
      String? verificationId,
      AccessToken? accessToken,
      apple.AuthorizationResult? appleCredential}) async {
    late auth.AuthCredential credential;
    switch (provider) {
      case AuthProviders.PASSWORD:
        credential = auth.EmailAuthProvider.credential(
            email: email!, password: password!);
        break;
      case AuthProviders.PHONE:
        credential = auth.PhoneAuthProvider.credential(
            smsCode: smsCode!, verificationId: verificationId!);
        break;
      case AuthProviders.FACEBOOK:
        credential =
            auth.FacebookAuthProvider.credential(accessToken!.tokenString);
        break;
      case AuthProviders.APPLE:
        credential = auth.OAuthProvider('apple.com').credential(
          accessToken: String.fromCharCodes(
              appleCredential!.credential?.authorizationCode ?? []),
          idToken: String.fromCharCodes(
              appleCredential.credential?.identityToken ?? []),
        );
        break;
    }
    return await auth.FirebaseAuth.instance.currentUser!
        .reauthenticateWithCredential(credential);
  }

  static resetPassword(String emailAddress) async =>
      await auth.FirebaseAuth.instance
          .sendPasswordResetEmail(email: emailAddress);

  static deleteUser() async {
    try {
      // delete user records from users table
      await firestore
          .collection(USERS)
          .doc(auth.FirebaseAuth.instance.currentUser!.uid)
          .delete();

      // delete user  from firebase auth
      await auth.FirebaseAuth.instance.currentUser!.delete();
    } catch (e, s) {
      print('FireStoreUtils.deleteUser $e $s');
    }
  }

  Future deleteOtherUser(String uid) async {
    try {
      // delete user records from REPORTS table
      await firestore
          .collection(REPORTS)
          .where('source', isEqualTo: uid)
          .get()
          .then((value) async {
        for (var doc in value.docs) {
          await firestore.doc(doc.reference.path).delete();
        }
      });

      // delete user records from REPORTS table
      await firestore
          .collection(REPORTS)
          .where('dest', isEqualTo: uid)
          .get()
          .then((value) async {
        for (var doc in value.docs) {
          await firestore.doc(doc.reference.path).delete();
        }
      });

      await firestore.collection(USERS).doc(uid).delete();

      HttpsCallable callable =
          FirebaseFunctions.instance.httpsCallable('deleteUser');
      final resp = await callable.call(<String, dynamic>{
        'uid': uid,
      });
      print("result: ${resp.data}");
    } catch (e, s) {
      print('FireStoreUtils.deleteUser $e $s');
    }
  }

  static Future<NotificationModel?> getNotificationContent(String type) async {
    NotificationModel? notificationModel;
    await firestore
        .collection(dynamicNotification)
        .where('type', isEqualTo: type)
        .get()
        .then((value) {
      print("------>");
      if (value.docs.isNotEmpty) {
        print(value.docs.first.data());

        notificationModel = NotificationModel.fromJson(value.docs.first.data());
      } else {
        notificationModel = NotificationModel(
            id: "",
            message: "Notification setup is pending",
            subject: "setup notification",
            type: "");
      }
    });
    return notificationModel;
  }

  static getPayFastSettingData() async {
    firestore
        .collection(Setting)
        .doc("payFastSettings")
        .get()
        .then((payFastData) {
      debugPrint(payFastData.data().toString());
      try {
        PayFastSettingData payFastSettingData =
            PayFastSettingData.fromJson(payFastData.data() ?? {});
        debugPrint(">122");
        debugPrint(payFastSettingData.toJson().toString());
        UserPreference.setPayFastData(payFastSettingData);
      } catch (error) {
        debugPrint("error>>>122");
        debugPrint(error.toString());
      }
    });
  }

  static getMercadoPagoSettingData() async {
    firestore.collection(Setting).doc("MercadoPago").get().then((mercadoPago) {
      try {
        MercadoPagoSettingData mercadoPagoDataModel =
            MercadoPagoSettingData.fromJson(mercadoPago.data() ?? {});
        UserPreference.setMercadoPago(mercadoPagoDataModel);
      } catch (error) {
        debugPrint(error.toString());
      }
    });
  }

  static getPaypalSettingData() async {
    firestore
        .collection(Setting)
        .doc("paypalSettings")
        .get()
        .then((paypalData) {
      try {
        PaypalSettingData paypalDataModel =
            PaypalSettingData.fromJson(paypalData.data() ?? {});
        UserPreference.setPayPalData(paypalDataModel);
      } catch (error) {
        debugPrint(error.toString());
      }
    });
  }

  static getFlutterWaveSettingData() async {
    firestore
        .collection(Setting)
        .doc("flutterWave")
        .get()
        .then((flutterWaveData) {
      try {
        FlutterWaveSettingData flutterWaveSettingData =
            FlutterWaveSettingData.fromJson(flutterWaveData.data() ?? {});
        UserPreference.setFlutterWaveData(flutterWaveSettingData);
      } catch (error) {
        debugPrint("error>>>122");
        debugPrint(error.toString());
      }
    });
  }

  static getPayStackSettingData() async {
    firestore.collection(Setting).doc("payStack").get().then((payStackData) {
      try {
        PayStackSettingData payStackSettingData =
            PayStackSettingData.fromJson(payStackData.data() ?? {});
        UserPreference.setPayStackData(payStackSettingData);
      } catch (error) {
        debugPrint("error>>>122");
        debugPrint(error.toString());
      }
    });
  }

  static getPaytmSettingData() async {
    firestore.collection(Setting).doc("PaytmSettings").get().then((paytmData) {
      try {
        PaytmSettingData paytmSettingData =
            PaytmSettingData.fromJson(paytmData.data() ?? {});
        UserPreference.setPaytmData(paytmSettingData);
      } catch (error) {
        debugPrint(error.toString());
      }
    });
  }

  static getWalletSettingData() {
    firestore
        .collection(Setting)
        .doc('walletSettings')
        .get()
        .then((walletSetting) {
      try {
        bool walletEnable = walletSetting.data()!['isEnabled'];

        UserPreference.setWalletData(walletEnable);
      } catch (e) {
        debugPrint(e.toString());
      }
    });
  }

  static getOrangeMoneySettingData() async {
    firestore
        .collection(Setting)
        .doc("orange_money_settings")
        .get()
        .then((payStackData) {
      try {
        OrangeMoney payStackSettingData =
            OrangeMoney.fromJson(payStackData.data() ?? {});
        UserPreference.setOrangeData(payStackSettingData);
      } catch (error) {
        print(error.toString());
      }
    });
  }

  static getXenditSettingData() async {
    firestore
        .collection(Setting)
        .doc("xendit_settings")
        .get()
        .then((payStackData) {
      try {
        Xendit payStackSettingData = Xendit.fromJson(payStackData.data() ?? {});

        UserPreference.setXenditData(payStackSettingData);
      } catch (error) {
        print(error.toString());
      }
    });
  }

  static getMidTransSettingData() async {
    firestore
        .collection(Setting)
        .doc("midtrans_settings")
        .get()
        .then((payStackData) {
      try {
        MidTrans payStackSettingData =
            MidTrans.fromJson(payStackData.data() ?? {});
        UserPreference.setMidTransData(payStackSettingData);
      } catch (error) {
        print(error.toString());
      }
    });
  }

  getRazorPayDemo() async {
    RazorPayModel userModel;
    firestore.collection(Setting).doc("razorpaySettings").get().then((user) {
      debugPrint(user.data().toString());
      try {
        userModel = RazorPayModel.fromJson(user.data() ?? {});
        UserPreference.setRazorPayData(userModel);
        RazorPayModel fhg = UserPreference.getRazorPayData();
        debugPrint(fhg.razorpayKey);
      } catch (e) {
        debugPrint(
            'FireStoreUtils.getUserByID failed to parse user object ${user.id}');
      }
    });
  }

  static Future<String?> associateCode() async {
    String usuarioID = auth.FirebaseAuth.instance.currentUser!.uid;
    QuerySnapshot<Map<String, dynamic>> snapshot = await FirebaseFirestore
        .instance
        .collection('codigo_entregador')
        .where('usuarioID', isEqualTo: usuarioID)
        .get();

    if (snapshot.docs.isNotEmpty) {
      return snapshot.docs.first.data()['codigo'];
    } else {
      String codigo = const Uuid().v4().substring(0, 11).toUpperCase();
      await FirebaseFirestore.instance
          .collection('codigo_entregador')
          .doc()
          .set({
            'codigo': codigo,
            'usuarioID': usuarioID,
          })
          .then((value) => print('Código associado com sucesso.'))
          .catchError((e) => print('Erro ao associar código: $e'));
      return codigo;
    }
  }

  static Stream<List<VendorModel>> associatedStores() async* {
    String usuarioID = auth.FirebaseAuth.instance.currentUser!.uid;
    try {
      QuerySnapshot<Map<String, dynamic>> userDoc = await FirebaseFirestore
          .instance
          .collection('vinculo_entregador_loja')
          .where('entregador_id', isEqualTo: usuarioID)
          .get();

      if (userDoc.docs.isNotEmpty) {
        List<VendorModel> lojas = [];
        for (var doc in userDoc.docs) {
          QuerySnapshot<Map<String, dynamic>> storeDoc = await FirebaseFirestore
              .instance
              .collection('vendors')
              .where('id', isEqualTo: doc.data()['loja_id'])
              .get();

          if (storeDoc.docs.isNotEmpty) {
            lojas.add(
                VendorModel.fromJson(storeDoc.docs.first.data(), doc.data()));
          }
        }
        yield lojas;
      } else {
        yield [];
      }
    } catch (e) {
      print('Erro ao buscar lojas associadas: $e');
    }
  }

  static Future<void> removeAssociatedStore(String storeId) async {
//  jogar para 0
    try {
      await FirebaseFirestore.instance
          .collection('vinculo_entregador_loja')
          .where('loja_id', isEqualTo: storeId)
          .get()
          .then((querySnapshot) {
        for (var doc in querySnapshot.docs) {
          doc.reference.update({'status_aceite_entregador': 0});
        }
      });
    } catch (e) {
      print(e);
    }
  }

  static Future approveStore(String id) async {
    await FirebaseFirestore.instance
        .collection('vinculo_entregador_loja')
        .where('loja_id', isEqualTo: id)
        .get()
        .then((querySnapshot) {
      for (var doc in querySnapshot.docs) {
        doc.reference.update({'status_aceite_entregador': 2});
      }
    });
  }
}
