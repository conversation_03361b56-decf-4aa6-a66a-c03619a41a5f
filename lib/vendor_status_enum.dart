import 'package:flutter/material.dart';

enum OrderStatus {
  // Pedido realizado
  // ORDER_STATUS_PLACED
  placed(1, 'Pedido realizado', Colors.blueAccent),

  // Aguardando pagamento do pedido
  // ORDER_STATUS_WAITING_PAYMENT
  waitingPayment(14, 'Aguardando pagamento', Colors.orangeAccent),

  // Pedido aceito
  // ORDER_STATUS_ACCEPTED
  confirmed(2, 'Pedido confirmado', Color.fromRGBO(105, 240, 174, 1)),

  // Pedido cancelado pelo usuário ou estabelecimento
  // ORDER_STATUS_CANCELLED
  cancelled(3, 'Pedido cancelado', Colors.redAccent),

  // Procurando entregador
  // ORDER_STATUS_DRIVER_SEARCHING
  driverSearching(4, 'Procurando entregador', Colors.deepOrangeAccent),

  // Entregador pendente
  // ORDER_STATUS_DRIVER_PENDING
  driverPending(5, 'Aguardando motorista', Colors.orangeAccent),

  // Entrega aceita pelo entregador
  // ORDER_STATUS_DRIVER_ACCEPTED
  driverAccepted(6, 'Entrega aceita', Colors.indigoAccent),

  // Entrega rejeitada pelo entregador
  // ORDER_STATUS_DRIVER_REJECTED
  driverRejected(7, 'Entrega rejeitada', Colors.redAccent),

  // Pedido em preparação no estabelecimento
  // ORDER_STATUS_PREPARING
  preparing(8, 'Pedido em preparação', Colors.cyanAccent),

  // Pedido enviado pelo estabelecimento
  // ORDER_STATUS_SHIPPED
  shipped(9, 'Pedido enviado', Colors.orangeAccent),

  // Entregador a caminho para retirar o pedido
  // ORDER_STATUS_DRIVER_ON_THE_WAY
  driverOnTheWay(10, 'Entrega a caminho', Colors.deepPurpleAccent),

  // Entregador chegando no local de retirada
  // ORDER_STATUS_DRIVER_ARRIVING
  driverArriving(11, 'Motorista chegando', Colors.tealAccent),

  // Pedido entregue
  // ORDER_STATUS_DELIVERED
  delivered(12, 'Pedido entregue', Colors.greenAccent),
  // Pedido concluído (entregue ao cliente)
  // ORDER_STATUS_COMPLETED
  completed(13, 'Pedido concluído', Color(0xff425799)),
  ;

  final int code;
  final String description;
  final Color color;

  const OrderStatus(this.code, this.description, this.color);

  static OrderStatus fromCode(int code) {
    return OrderStatus.values.firstWhere(
      (status) => status.code == code,
      orElse: () => throw ArgumentError('Código de status inválido: $code'),
    );
  }

  static OrderStatus fromString(String description) {
    return OrderStatus.values.firstWhere(
      (status) => status.description.toLowerCase() == description.toLowerCase(),
      orElse: () =>
          throw ArgumentError('Descrição de status inválida: $description'),
    );
  }

  static Color getColorFromCode(String value) {
    return OrderStatus.fromString(value).color;
  }
}
