// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAAD-uCpKgARZr8-iWmVQhoNLRNP7mpMLY',
    appId: '1:517563732136:android:30c6685412a1a95097f29d',
    messagingSenderId: '517563732136',
    projectId: 'rapidoladev',
    databaseURL: 'https://rapidoladev-default-rtdb.firebaseio.com',
    storageBucket: 'rapidoladev.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyADk2H6VZOTFW5nXRmXIRwodlmuC4v5Kbk',
    appId: '1:517563732136:ios:584fe9e0e4ae736f97f29d',
    messagingSenderId: '517563732136',
    projectId: 'rapidoladev',
    databaseURL: 'https://rapidoladev-default-rtdb.firebaseio.com',
    storageBucket: 'rapidoladev.appspot.com',
    androidClientId:
        '517563732136-3qla689sqcc1rm64fs7jv8ddlh4qktkd.apps.googleusercontent.com',
    iosClientId:
        '517563732136-50vm8cujis9c0kpmfkq30nu5ue8hgofv.apps.googleusercontent.com',
    iosBundleId: 'com.Ta entregue.driver',
  );
}
