import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/variant_info.dart';

class ProductModel {
  dynamic extras;
  dynamic variant_info;
  String? extras_price;
  String id;
  String name;
  String photo;
  String price;
  String discount_price;
  int quantity;
  String vendorID;
  String category_id;

  ProductModel(
      {this.id = '',
      this.photo = '',
      this.price = '',
      this.discount_price = '0.0',
      this.name = '',
      this.quantity = 0,
      this.vendorID = '',
      this.category_id = '',
      this.extras = const [],
      this.extras_price = "0.0",
      this.variant_info});

  factory ProductModel.fromJson(Map<String, dynamic> parsedJson) {
    dynamic extrasVal;
    if (parsedJson['extras'] == null) {
      extrasVal = List<String>.empty();
    } else {
      if (parsedJson['extras'] is String) {
        if (parsedJson['extras'] == '[]') {
          extrasVal = List<String>.empty();
        } else {
          String extraDecode = parsedJson['extras']
              .toString()
              .replaceAll("[", "")
              .replaceAll("]", "")
              .replaceAll("\"", "");
          if (extraDecode.contains(",")) {
            extrasVal = extraDecode.split(",");
          } else {
            extrasVal = [extraDecode];
          }
        }
      }
      if (parsedJson['extras'] is List) {
        extrasVal = parsedJson['extras'].cast<String>();
      }
    }

    int quanVal = 0;
    if (parsedJson['quantity'] == null ||
        parsedJson['quantity'] == double.infinity) {
      quanVal = 0;
    } else {
      if (parsedJson['quantity'] is String) {
        quanVal = int.parse(parsedJson['quantity']);
      } else {
        quanVal = (parsedJson['quantity'] is double)
            ? (parsedJson["quantity"].isNaN
                ? 0
                : (parsedJson['quantity'] as double).toInt())
            : parsedJson['quantity'];
      }
    }
    return ProductModel(
      id: parsedJson['id'] ?? '',
      photo: parsedJson['photo'] == '' ? placeholderImage : parsedJson['photo'],
      price: parsedJson['price'] ?? '0.0',
      discount_price: parsedJson['discount_price'] ?? '0.0',
      quantity: quanVal,
      name: parsedJson['name'] ?? '',
      vendorID: parsedJson['vendorID'] ?? '',
      category_id: parsedJson['category_id'] ?? '',
      extras: extrasVal,
      extras_price: parsedJson["extras_price"] ?? "0.0",
      variant_info: (parsedJson.containsKey('variant_info') &&
              parsedJson['variant_info'] != null)
          ? VariantInfo.fromJson(parsedJson['variant_info'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'photo': photo,
      'price': price,
      'discount_price': discount_price,
      'name': name,
      'quantity': quantity,
      'vendorID': vendorID,
      'category_id': category_id,
      "extras": extras,
      "extras_price": extras_price,
      'variant_info': variant_info?.toJson(),
    };
  }
}
