import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/AddressModel.dart';
import 'package:emartdriver/model/CabOrderModel.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:flutter/material.dart';

class User with ChangeNotifier {
  String rideType;
  String email;
  String? cpfCnpj;
  String? cpf;
  String? cnpj;
  String firstName;

  String lastName;

  UserSettings settings;

  String phoneNumber;

  bool active;
  bool isActive;

  Timestamp lastOnlineTimestamp;

  String userID;

  String profilePictureURL;
  String carProofPictureURL;
  String driverProofPictureURL;

  String appIdentifier;

  String fcmToken;

  UserLocation location;

  List<AddressModel>? shippingAddress = [];

  String role;

  String carName;

  String carNumber;
  String carColor;

  String carPictureURL;

  String? inProgressOrderID;

  OrderModel? orderRequestData;
  CabOrderModel? ordercabRequestData;

  UserBankDetails userBankDetails;
  GeoFireData geoFireData;
  GeoPoint coordinates;
  String serviceType;
  String vehicleType;
  String vehicleId;
  String carMakes;

  num walletAmount;
  num? rotation;
  num reviewsCount;
  num reviewsSum;
  String driverRate;
  String carRate;
  String? sectionId;
  CarInfo? carInfo;
  List<dynamic>? rentalBookingDate;
  Timestamp? createdAt;
  bool? teste_entregador;

  String? documentUrl;
  DocumentStatus? document_accept;
  String? criminalRecordUrl;
  DocumentStatus? criminalRecord_accept;
  String? proofOfAddressUrl;
  DocumentStatus? proofOfAddress_accept;
  bool? released_for_use;
  User({
    this.email = '',
    this.rideType = '',
    this.userID = '',
    this.profilePictureURL = '',
    this.carProofPictureURL = '',
    this.driverProofPictureURL = '',
    this.firstName = '',
    this.phoneNumber = '',
    this.lastName = '',
    this.active = false,
    this.isActive = false,
    lastOnlineTimestamp,
    settings,
    this.fcmToken = '',
    location,
    this.shippingAddress,
    this.role = USER_ROLE_DRIVER,
    this.carName = 'Uber Car',
    this.carNumber = 'No Plates',
    this.carColor = '',
    this.carPictureURL = DEFAULT_CAR_IMAGE,
    this.inProgressOrderID,
    this.walletAmount = 0.0,
    this.serviceType = "",
    this.vehicleType = "",
    this.vehicleId = "",
    this.carMakes = "",
    this.rotation,
    this.rentalBookingDate,
    this.reviewsCount = 0,
    this.reviewsSum = 0,
    this.driverRate = "0",
    this.carRate = "0",
    userBankDetails,
    geoFireData,
    coordinates,
    carInfo,
    this.orderRequestData,
    this.ordercabRequestData,
    this.createdAt,
    this.sectionId,
    this.teste_entregador = true,
    this.released_for_use,
    this.documentUrl,
    this.document_accept,
    this.criminalRecordUrl,
    this.criminalRecord_accept,
    this.proofOfAddressUrl,
    this.proofOfAddress_accept,
    this.cpfCnpj,
    this.cpf,
    this.cnpj,
  })  : lastOnlineTimestamp = lastOnlineTimestamp ?? Timestamp.now(),
        settings = settings ?? UserSettings(),
        appIdentifier = 'Ta entregue Driver ${Platform.operatingSystem}',
        userBankDetails = userBankDetails ?? UserBankDetails(),
        location = location ?? UserLocation(),
        coordinates = coordinates ?? const GeoPoint(0.0, 0.0),
        carInfo = carInfo ?? CarInfo(),
        geoFireData = geoFireData ??
            GeoFireData(
              geohash: "",
              geoPoint: const GeoPoint(0.0, 0.0),
            );

  String fullName() {
    return '$firstName $lastName';
  }

  factory User.fromJson(Map<String, dynamic> parsedJson) {
    List<AddressModel>? shippingAddressList = [];
    if (parsedJson['shippingAddress'] != null) {
      shippingAddressList = <AddressModel>[];
      parsedJson['shippingAddress'].forEach((v) {
        shippingAddressList!.add(AddressModel.fromJson(v));
      });
    }
    return User(
      released_for_use: parsedJson['released_for_use'] ?? false,
      documentUrl: parsedJson['documentUrl'] ?? '',
      cpfCnpj: parsedJson['cpfCnpj'],
      cpf: parsedJson['cpf'],
      cnpj: parsedJson['cnpj'],
      document_accept: DocumentStatus.fromCode(parsedJson['document_accept']),
      criminalRecordUrl: parsedJson['criminalRecordUrl'] ?? '',
      criminalRecord_accept:
          DocumentStatus.fromCode(parsedJson['criminalRecord_accept']),
      proofOfAddressUrl: parsedJson['proofOfAddressUrl'] ?? '',
      proofOfAddress_accept:
          DocumentStatus.fromCode(parsedJson['proofOfAddress_accept']),
      email: parsedJson['email'] ?? '',
      rideType: parsedJson['rideType'] ?? '',
      walletAmount: parsedJson['wallet_amount'] ?? 0.0,
      userBankDetails: parsedJson.containsKey('userBankDetails')
          ? UserBankDetails.fromJson(parsedJson['userBankDetails'])
          : UserBankDetails(),
      firstName: parsedJson['firstName'] ?? '',
      lastName: parsedJson['lastName'] ?? '',
      geoFireData: parsedJson.containsKey('g')
          ? GeoFireData.fromJson(parsedJson['g'])
          : GeoFireData(
              geohash: "",
              geoPoint: const GeoPoint(0.0, 0.0),
            ),
      coordinates: parsedJson['coordinates'] ?? const GeoPoint(0.0, 0.0),
      isActive: parsedJson['isActive'] ?? false,
      rotation: parsedJson['rotation'] ?? 0.0,
      active: parsedJson['active'] ?? true,
      vehicleType: parsedJson['vehicleType'] ?? '',
      vehicleId: parsedJson['vehicleId'] ?? '',
      carMakes: parsedJson['carMakes'] ?? '',
      lastOnlineTimestamp: parsedJson['lastOnlineTimestamp'],
      settings: parsedJson.containsKey('settings')
          ? UserSettings.fromJson(parsedJson['settings'])
          : UserSettings(),
      phoneNumber: parsedJson['phoneNumber'] ?? '',
      userID: parsedJson['id'] ?? parsedJson['userID'] ?? '',
      profilePictureURL: parsedJson['profilePictureURL'] ?? '',
      driverProofPictureURL: parsedJson['driverProofPictureURL'] ?? '',
      carProofPictureURL: parsedJson['carProofPictureURL'] ?? '',
      fcmToken: parsedJson['fcmToken'] ?? '',
      serviceType: parsedJson['serviceType'] ?? '',
      driverRate: parsedJson['driverRate'] ?? '0',
      carRate: parsedJson['carRate'] ?? '0',
      rentalBookingDate: parsedJson['rentalBookingDate'] ?? [],
      carInfo: parsedJson.containsKey('carInfo')
          ? CarInfo.fromJson(parsedJson['carInfo'])
          : CarInfo(),
      location: parsedJson.containsKey('location')
          ? UserLocation.fromJson(parsedJson['location'])
          : UserLocation(),
      shippingAddress: shippingAddressList,
      role: parsedJson['role'] ?? '',
      carName: parsedJson['carName'] ?? '',
      carNumber: parsedJson['carNumber'] ?? '',
      carColor: parsedJson['carColor'] ?? '',
      carPictureURL: parsedJson['carPictureURL'] ?? '',
      inProgressOrderID: parsedJson['inProgressOrderID'],
      reviewsCount: parsedJson['reviewsCount'] ?? 0,
      reviewsSum: parsedJson['reviewsSum'] ?? 0,
      sectionId: parsedJson['sectionId'] ?? '',
      createdAt: parsedJson['createdAt'],
      teste_entregador: parsedJson['teste_entregador'] ?? false,
    );
  }

  factory User.fromPayload(Map<String, dynamic> parsedJson) {
    List<AddressModel>? shippingAddressList = [];
    if (parsedJson['shippingAddress'] != null) {
      shippingAddressList = <AddressModel>[];
      parsedJson['shippingAddress'].forEach((v) {
        shippingAddressList!.add(AddressModel.fromJson(v));
      });
    }
    return User(
      released_for_use: parsedJson['released_for_use'] ?? false,
      documentUrl: parsedJson['documentUrl'] ?? '',
      cpfCnpj: parsedJson['cpfCnpj'],
      cpf: parsedJson['cpf'],
      cnpj: parsedJson['cnpj'],
      document_accept: DocumentStatus.fromCode(parsedJson['document_accept']),
      criminalRecordUrl: parsedJson['criminalRecordUrl'] ?? '',
      criminalRecord_accept:
          DocumentStatus.fromCode(parsedJson['criminalRecord_accept']),
      proofOfAddressUrl: parsedJson['proofOfAddressUrl'] ?? '',
      proofOfAddress_accept:
          DocumentStatus.fromCode(parsedJson['proofOfAddress_accept']),
      rideType: parsedJson['rideType'] ?? '',
      email: parsedJson['email'] ?? '',
      firstName: parsedJson['firstName'] ?? '',
      lastName: parsedJson['lastName'] ?? '',
      walletAmount: parsedJson['wallet_amount'] ?? 0.0,
      rotation: parsedJson['rotation'] ?? 0.0,
      userBankDetails: parsedJson.containsKey('userBankDetails')
          ? UserBankDetails.fromJson(parsedJson['userBankDetails'])
          : UserBankDetails(),
      isActive: parsedJson['isActive'] ?? false,
      active: parsedJson['active'] ?? true,
      serviceType: parsedJson['serviceType'] ?? '',
      geoFireData: parsedJson.containsKey('g')
          ? GeoFireData.fromJson(parsedJson['g'])
          : GeoFireData(
              geohash: "",
              geoPoint: const GeoPoint(0.0, 0.0),
            ),
      coordinates: parsedJson['coordinates'] ?? const GeoPoint(0.0, 0.0),
      lastOnlineTimestamp: Timestamp.fromMillisecondsSinceEpoch(
          parsedJson['lastOnlineTimestamp']),
      settings: parsedJson.containsKey('settings')
          ? UserSettings.fromJson(parsedJson['settings'])
          : UserSettings(),
      phoneNumber: parsedJson['phoneNumber'] ?? '',
      userID: parsedJson['id'] ?? parsedJson['userID'] ?? '',
      profilePictureURL: parsedJson['profilePictureURL'] ?? '',
      driverProofPictureURL: parsedJson['driverProofPictureURL'] ?? '',
      carProofPictureURL: parsedJson['carProofPictureURL'] ?? '',
      fcmToken: parsedJson['fcmToken'] ?? '',
      location: parsedJson.containsKey('location')
          ? UserLocation.fromJson(parsedJson['location'])
          : UserLocation(),
      shippingAddress: shippingAddressList,
      role: parsedJson['role'] ?? '',
      carName: parsedJson['carName'] ?? '',
      carNumber: parsedJson['carNumber'] ?? '',
      carColor: parsedJson['carColor'] ?? '',
      vehicleType: parsedJson['vehicleType'] ?? '',
      vehicleId: parsedJson['vehicleId'] ?? '',
      carMakes: parsedJson['carMakes'] ?? '',
      carPictureURL: parsedJson['carPictureURL'] ?? '',
      inProgressOrderID: parsedJson['inProgressOrderID'],
      reviewsCount: parsedJson['reviewsCount'] ?? 0,
      reviewsSum: parsedJson['reviewsSum'] ?? 0,
      driverRate: parsedJson['driverRate'] ?? '',
      carRate: parsedJson['carRate'] ?? '',
      sectionId: parsedJson['sectionId'] ?? '',
      rentalBookingDate: parsedJson['rentalBookingDate'] ?? [],
      teste_entregador: parsedJson['teste_entregador'] ?? true,
      orderRequestData: parsedJson.containsKey('orderRequestData') &&
              parsedJson['orderRequestData'] != null
          ? OrderModel.fromJson(parsedJson['orderRequestData'])
          : null,
      ordercabRequestData: parsedJson.containsKey('ordercabRequestData') &&
              parsedJson['ordercabRequestData'] != null
          ? CabOrderModel.fromJson(parsedJson['ordercabRequestData'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = {
      'email': email,
      'rideType': rideType,
      'cpfCnpj': cpfCnpj,
      'cpf': cpf,
      'cnpj': cnpj,
      'firstName': firstName,
      'lastName': lastName,
      'settings': settings.toJson(),
      'phoneNumber': phoneNumber,
      'id': userID,
      'isActive': isActive,
      'active': active,
      'lastOnlineTimestamp': lastOnlineTimestamp,
      'profilePictureURL': profilePictureURL,
      'appIdentifier': appIdentifier,
      'fcmToken': fcmToken,
      'shippingAddress': shippingAddress?.map((v) => v.toJson()).toList(),
      'role': role,
      'createdAt': createdAt,
      'teste_entregador': teste_entregador,
      'documentUrl': documentUrl,
      'document_accept': document_accept?.value,
      'criminalRecordUrl': criminalRecordUrl,
      'criminalRecord_accept': criminalRecord_accept?.value,
      'proofOfAddressUrl': proofOfAddressUrl,
      'proofOfAddress_accept': proofOfAddress_accept?.value,
      'released_for_use': released_for_use,
    };

    return json;
  }
}

class UserSettings {
  bool pushNewMessages;

  bool orderUpdates;

  bool newArrivals;

  bool promotions;

  UserSettings(
      {this.pushNewMessages = true,
      this.orderUpdates = true,
      this.newArrivals = true,
      this.promotions = true});

  factory UserSettings.fromJson(Map<dynamic, dynamic> parsedJson) {
    return UserSettings(
      pushNewMessages: parsedJson['pushNewMessages'] ?? true,
      orderUpdates: parsedJson['orderUpdates'] ?? true,
      newArrivals: parsedJson['newArrivals'] ?? true,
      promotions: parsedJson['promotions'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pushNewMessages': pushNewMessages,
      'orderUpdates': orderUpdates,
      'newArrivals': newArrivals,
      'promotions': promotions,
    };
  }
}

class UserLocation {
  double latitude;

  double longitude;

  UserLocation({this.latitude = 0.01, this.longitude = 0.01});

  factory UserLocation.fromJson(Map<dynamic, dynamic> parsedJson) {
    return UserLocation(
      latitude: parsedJson['latitude'] ?? 00.1,
      longitude: parsedJson['longitude'] ?? 00.1,
    );
  }

  GeoPoint get geoPoint {
    return GeoPoint(latitude, longitude);
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

class GeoFireData {
  String? geohash;
  GeoPoint? geoPoint;

  GeoFireData({this.geohash, this.geoPoint});

  factory GeoFireData.fromJson(Map<dynamic, dynamic> parsedJson) {
    return GeoFireData(
      geohash: parsedJson['geohash'] ?? '',
      geoPoint: parsedJson['geopoint'] ?? const GeoPoint(0.0, 0.0),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'geohash': geohash,
      'geopoint': geoPoint,
    };
  }
}

class UserBankDetails {
  String bankName;

  String branchName;

  String holderName;

  String accountNumber;

  String otherDetails;

  UserBankDetails({
    this.bankName = '',
    this.otherDetails = '',
    this.branchName = '',
    this.accountNumber = '',
    this.holderName = '',
  });

  factory UserBankDetails.fromJson(Map<String, dynamic> parsedJson) {
    return UserBankDetails(
      bankName: parsedJson['bankName'] ?? '',
      branchName: parsedJson['branchName'] ?? '',
      holderName: parsedJson['holderName'] ?? '',
      accountNumber: parsedJson['accountNumber'] ?? '',
      otherDetails: parsedJson['otherDetails'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'bankName': bankName,
      'branchName': branchName,
      'holderName': holderName,
      'accountNumber': accountNumber,
      'otherDetails': otherDetails,
    };
  }

  Map<String, dynamic> toPayload() {
    return {
      'bankName': bankName,
      'branchName': branchName,
      'holderName': holderName,
      'accountNumber': accountNumber,
      'otherDetails': otherDetails,
    };
  }
}

class CarInfo {
  String? passenger;
  String? doors;
  String? carName;
  String? airConditioning;
  String? gear;
  String? mileage;
  String? fuelFilling;
  String? fuelType;
  String? maxPower;
  String? mph;
  String? topSpeed;
  List<dynamic>? carImage;

  CarInfo({
    this.passenger,
    this.doors,
    this.carName,
    this.airConditioning,
    this.gear,
    this.mileage,
    this.fuelFilling,
    this.fuelType,
    this.carImage,
    this.maxPower,
    this.mph,
    this.topSpeed,
  });

  CarInfo.fromJson(Map<String, dynamic> json) {
    passenger = json['passenger'] ?? "";
    doors = json['doors'] ?? "";
    carName = json['carName'] ?? "";
    airConditioning = json['air_conditioning'] ?? "";
    gear = json['gear'] ?? "";
    mileage = json['mileage'] ?? "";
    fuelFilling = json['fuel_filling'] ?? "";
    fuelType = json['fuel_type'] ?? "";
    carImage = json['car_image'] ?? [];
    maxPower = json['maxPower'] ?? "";
    mph = json['mph'] ?? "";
    topSpeed = json['topSpeed'] ?? "";
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['passenger'] = passenger;
    data['doors'] = doors;
    data['carName'] = carName;
    data['air_conditioning'] = airConditioning;
    data['gear'] = gear;
    data['mileage'] = mileage;
    data['fuel_filling'] = fuelFilling;
    data['fuel_type'] = fuelType;
    data['car_image'] = carImage;
    data['maxPower'] = maxPower;
    data['mph'] = mph;
    data['topSpeed'] = topSpeed;
    return data;
  }
}

enum DocumentStatus {
  sent(0, 'Enviado', color: Colors.grey),
  received(1, 'Recebido', color: Colors.blue),
  inAnalysis(2, 'Em análise', color: Colors.orangeAccent),
  rejected(3, 'Reprovado', color: Colors.redAccent),
  approved(4, 'Aprovado', color: Colors.greenAccent);

  final int value;
  final String status;
  final Color color;

  const DocumentStatus(this.value, this.status,
      {this.color = Colors.redAccent});

  static DocumentStatus fromCode(int? code) {
    if (code == null) {
      return DocumentStatus.sent;
    }
    return DocumentStatus.values.firstWhere(
      (status) => status.value == code,
      orElse: () => DocumentStatus.sent,
    );
  }
}
