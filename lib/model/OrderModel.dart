import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/model/User.dart';
import 'package:emartdriver/model/VendorModel.dart';
import 'package:emartdriver/vendor_status_enum.dart';

class OrderModel {
  final String authorID;
  final User author;
  final User? driver;
  final String? driverID;
  final Timestamp createdAt;
  final String vendorID;
  final String? sectionId;
  final VendorModel vendor;
  final OrderStatus? status;
  final String id;
  final num? discount;
  final String? couponCode;
  final String? couponId;
  final String? notes;
  final String paymentMethod;
  final String paymentStatus;
  final String? deliveryCharge;
  final Map<String, dynamic>? specialDiscount;
  final String courierCompanyName;
  final String courierTrackingId;
  final String? estimatedTimeToPrepare;
  final Timestamp? scheduleTime;
  final String? pixPaymentReceipt;
  final int? storeConfirmationCode;
  final int? deliveryConfirmationCode;
  final bool? takeAway;
  final List<Map<String, dynamic>>? products;
  final String? entregador_id;
  final bool has_return;
  final String? price;
  final String? payValueDriver;
  final String? distance;
  const OrderModel({
    required this.author,
    required this.authorID,
    required this.createdAt,
    required this.vendor,
    required this.vendorID,
    required this.id,
    required this.paymentMethod,
    this.driver,
    this.driverID,
    this.sectionId,
    this.status,
    this.discount = 0,
    this.couponCode = '',
    this.couponId = '',
    this.notes = '',
    this.paymentStatus = 'Aguardando Pagamento',
    this.deliveryCharge,
    this.specialDiscount,
    this.courierCompanyName = '',
    this.courierTrackingId = '',
    this.estimatedTimeToPrepare,
    this.scheduleTime,
    this.pixPaymentReceipt,
    this.storeConfirmationCode,
    this.deliveryConfirmationCode,
    this.takeAway = false,
    this.products,
    this.entregador_id,
    this.has_return = false,
    this.price,
    this.payValueDriver,
    this.distance,
  });

  factory OrderModel.fromJson(Map<String, dynamic> parsedJson) {
    num discountVal = 0;
    if (parsedJson['discount'] == null) {
      discountVal = 0;
    } else if (parsedJson['discount'] is String) {
      discountVal = double.parse(parsedJson['discount']);
    } else {
      discountVal = parsedJson['discount'];
    }

    return OrderModel(
      payValueDriver: parsedJson['payValueDriver'],
      author: parsedJson.containsKey('author')
          ? User.fromJson(parsedJson['author'])
          : User(),
      authorID: parsedJson['authorID'] ?? '',
      createdAt: parsedJson['createdAt'] ?? Timestamp.now(),
      id: parsedJson['id'] ?? '',
      vendor: parsedJson.containsKey('vendor')
          ? VendorModel.fromJson(parsedJson['vendor'])
          : VendorModel(),
      vendorID: parsedJson['vendorID'] ?? '',
      paymentMethod: parsedJson['paymentMethod'] ?? '',
      discount: discountVal,
      couponCode: parsedJson['couponCode'] ?? '',
      couponId: parsedJson['couponId'] ?? '',
      notes: parsedJson['notes'] ?? '',
      paymentStatus: parsedJson['paymentStatus'] ?? 'Aguardando Pagamento',
      deliveryCharge: parsedJson['deliveryCharge'],
      specialDiscount: parsedJson['specialDiscount'] ?? {},
      courierCompanyName: parsedJson['courierCompanyName'] ?? '',
      courierTrackingId: parsedJson['courierTrackingId'] ?? '',
      estimatedTimeToPrepare: parsedJson['estimatedTimeToPrepare'],
      scheduleTime: parsedJson['scheduleTime'],
      pixPaymentReceipt: parsedJson['pixPaymentReceipt'],
      storeConfirmationCode: parsedJson['storeConfirmationCode'],
      deliveryConfirmationCode: parsedJson['deliveryConfirmationCode'],
      takeAway: parsedJson['takeAway'] ?? false,
      products: parsedJson.containsKey('products')
          ? List<Map<String, dynamic>>.from(parsedJson['products'])
          : [],
      status: OrderStatus.fromString(parsedJson['status']),
      driver: parsedJson.containsKey('driver')
          ? User.fromJson(parsedJson['driver'])
          : null,
      driverID: parsedJson['driverID'] ?? '',
      sectionId: parsedJson['sectionId'] ?? '',
      entregador_id: parsedJson['entregador_id'] ?? '',
      has_return: parsedJson['has_return'] ?? false,
      price: parsedJson['price'] ?? '',
      distance: parsedJson['distance'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'author': author.toJson(),
      'authorID': authorID,
      'createdAt': createdAt,
      'id': id,
      'vendor': vendor.toJson(),
      'vendorID': vendorID,
      'paymentMethod': paymentMethod,
      'status': status,
      'discount': discount,
      'couponCode': couponCode,
      'couponId': couponId,
      'notes': notes,
      'paymentStatus': paymentStatus,
      'deliveryCharge': deliveryCharge,
      'specialDiscount': specialDiscount,
      'courierCompanyName': courierCompanyName,
      'courierTrackingId': courierTrackingId,
      'estimatedTimeToPrepare': estimatedTimeToPrepare,
      'scheduleTime': scheduleTime,
      'pixPaymentReceipt': pixPaymentReceipt,
      'storeConfirmationCode': storeConfirmationCode,
      'deliveryConfirmationCode': deliveryConfirmationCode,
      'takeAway': takeAway,
      'products': products ?? [],
      'driver': driver?.toJson(),
      'driverID': driverID,
      'sectionId': sectionId,
      'entregador_id': entregador_id,
      'has_return': has_return,
      'price': price,
      'payValueDriver': payValueDriver,
      'distance': distance,
    };
  }
}
