import 'package:emartdriver/model/User.dart';

class AddressModel {
  String? id;
  String? rua;
  String? address;
  String? addressAs;
  String? landmark;
  String? locality;
  UserLocation? location;
  bool? isDefault;
  String? bairro;
  String? cep;
  String? cidade;
  String? complemento;
  String? estado;
  String? logradouro;
  String? numero;
  String? pais;
  String? referencia;

  AddressModel(
      {this.id,
      this.rua,
      this.address,
      this.addressAs,
      this.landmark,
      this.locality,
      this.location,
      this.isDefault,
      this.bairro,
      this.cep,
      this.cidade,
      this.complemento,
      this.estado,
      this.logradouro,
      this.numero,
      this.pais,
      this.referencia});

  AddressModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    rua = json['rua'];
    address = json['address'];
    addressAs = json['addressAs'];
    landmark = json['landmark'];
    locality = json['locality'];
    isDefault = json['isDefault'];
    bairro = json['bairro'];
    cep = json['cep'];
    cidade = json['cidade'];
    complemento = json['complemento'];
    estado = json['estado'];
    logradouro = json['logradouro'];
    numero = json['numero'];
    pais = json['pais'];
    referencia = json['referencia'];
    location = json['location'] == null
        ? null
        : UserLocation.fromJson(json['location']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['rua'] = this.rua;
    data['address'] = this.address;
    data['addressAs'] = this.addressAs;
    data['landmark'] = this.landmark;
    data['locality'] = this.locality;
    data['isDefault'] = this.isDefault;
    data['bairro'] = this.bairro;
    data['cep'] = this.cep;
    data['cidade'] = this.cidade;
    data['complemento'] = this.complemento;
    data['estado'] = this.estado;
    data['logradouro'] = this.logradouro;
    data['numero'] = this.numero;
    data['pais'] = this.pais;
    data['referencia'] = this.referencia;
    if (this.location != null) {
      data['location'] = this.location!.toJson();
    }
    return data;
  }

  String getFullAddress() {
    print(address);
    print(locality);
    print(landmark);
    return '${address == null || address!.isEmpty ? "" : address} $locality ${landmark == null || landmark!.isEmpty ? "" : landmark.toString()}';
  }
}
